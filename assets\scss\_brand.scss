.brand-wrapper {
    background-color: $white;
    border: 1px solid rgba(124, 138, 166, 0.15);
    box-shadow: 0px 10px 16px 0px rgba(0, 0, 0, 0.04);
    padding: 60px 55px 25px;
    margin-top: -75px;
    position: relative;
    z-index: 9;

    .brand-logo {
        text-align: center;
        padding: 30px 0;
        background-color: rgba(255, 255, 255, 0.80);
        box-shadow: (0px 4px 12px rgba(0, 0, 0, 0.06));
        margin-bottom: 35px;
        margin-top: 5px;

        &.style-2 {
            margin-top: 0;
        }
    }
}

.brand-wrapper-3 {
    padding: 45px 0;
    border-bottom: 1px dashed #E2DFE3;

    .brand-image {
        text-align: center;
    }
}

.brand-wrapper-4 {
    margin: 0 55px;
    border-radius: 0 0 25px 25px;
    height: 715px;
    padding-top: 362px;

    @include breakpoint (max-xxl){
        margin: 0 20px;
    }

    @include breakpoint (max-md){
       padding-top: 310px;
    }

    h4 {
        margin-top: 50px;

        span {
            color: #FF1B1B;
        }
    }

    .brand-items {
        border: 1px dashed #252525;
        padding: 0 25px;
        margin-top: 50px;
        @include flex;
        justify-content: space-between;
        margin-bottom: 50px;
        position: relative;

        @include breakpoint (max-xl){
            flex-wrap: wrap;
            gap: 30px;
            justify-content: start;
            border: none;
        }

        @include breakpoint (max-md){
           margin-top: 20px;
        }

       .brand-logo {
            padding: 45px 0;

            @include breakpoint (max-xl){
                padding: 0;
            }

            &:not(:last-child){
                border-right: 1px dashed #252525;
                padding-right: 50px;

                @include breakpoint (max-xl){
                   border: none;
                   padding-right: 0;
                }
            }
       }
    }
}