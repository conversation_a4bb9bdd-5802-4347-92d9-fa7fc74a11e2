.footer-widgets-wrapper {
    padding: 100px 0 123px;
    position: relative;
    z-index: 9;


    @include breakpoint(max-xl){
        padding: 60px 0 90px;
    }

    @include breakpoint(max-lg){
        padding: 50px 0 70px;
    }

    .single-footer-widget {
        margin-top: 30px;

        .widget-head {
            margin-bottom: 30px;

            @include breakpoint (max-sm){
               margin-bottom: 20px;
            }

            h3 {
                font-weight: bold;
                color: $header-color;
                font-size: 22px;
                display: inline-block;
            }
        }

        .footer-content {
            p {
                color: $header-color;
            }

            .contact-list {
                margin-top: 30px;

                li {
                    font-size: 18px;

                    &:not(:last-child){
                        margin-bottom: 15px;
                    }

                    i {
                        color: $theme-color;
                        margin-right: 10px;
                    }

                    a {
                        color: #4F5055;
                    }
                }
            }

           .scan-items {
                @include flex;
                gap: 13px;
                flex-wrap: wrap;

                .scan-img {
                    background-color: $white;
                    border-radius: 10px;
                    box-shadow: 0px 1px 17px 0px rgba(1, 16, 61, 0.09);
                    display: inline-block;
                    width: 100px;
                    height: 100px;
                    line-height: 100px;
                    text-align: center;
                }

                .store-list {
                    
                    li {
                        font-size: 14px;
                        font-weight: 700;
                        font-family: $heading-font;

                        &:not(:last-child){
                            margin-bottom: 16px;
                        }
                       
                        a {
                            @include flex;
                            color: $white;
                            padding: 13px 35px;
                            border-radius: 5px;
                            background: #01103D;
                            box-shadow: 0px 1px 2px 0px rgba(1, 16, 61, 0.14);
                            gap: 10px;
                            line-height: 1;
                        }

                        &.active {
                            a {
                                color: #01103D;
                                background: $white;
                            }
                        }
                    }
                }
           }

           .brand-logo {
                margin-top: 30px;

                img {
                    max-width: 100%;
                }
           }
        }

       .list-items {
            li {
                @include transition;
                font-weight: 500;

                &:not(:last-child){
                    margin-bottom: 15px;
                }

                a {
                    color: $header-color;
                    font-weight: 500;
                }

                &:hover {
                    margin-left: 5px;
                    a {
                        color: $theme-color-2;
                    }
                }
            }
       }
    }

    &.style-2 {
        .widget-head {
            h3 {
                color: $white;
            }
        }
        .footer-content {
            p {
                font-weight: 400;
            }

            .contact-list {
                margin-top: 15px;

               li {
                    a {
                        color: $white;
                    }
               }
            }

            .social-icon {
                margin-top: 40px;
                gap: 15px;
                position: relative;
                z-index: 9;

                @include breakpoint (max-sm){
                    margin-top: 20px;
                }

            a {
                width: 40px;
                height: 40px;
                line-height: 40px;
                text-align: center;
                font-size: 16px;
                display: block;
                color: $white;
                transition: all .4s ease-in-out;
                text-align: center;
                background-color: transparent;
                border: 1px solid #2C2E34;
                border-radius: 50%;

                    &:hover {
                        background-color: $theme-color;
                        color: $white;
                        border: 1px solid transparent
                    }
                }
            }
        }

        .list-items {
            li {

                a {
                    color: $white;
                    font-weight: 500;
                }
            }
       }
   }
}

.footer-bottom {
    border-top: 1px solid rgba(86, 101, 139, 0.10);
    padding: 30px 0;
    position: relative;

    .scroll-icon {
        width: 60px;
        height: 60px;
        line-height: 60px;
        border-radius: 14px;
        background-color: $theme-color;
        color: $white;
        display: inline-block;
        text-align: center;
        @include transition;
        position: absolute;
        top: -30px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 99;

        &:hover {
            background-color: $header-color;
        }

        @include breakpoint (max-lg){
            top: -80px;
            left: 57%;
        }
    }

    .footer-wrapper {
        position: relative;
        z-index: 9;

        @include breakpoint (max-md){
            flex-wrap: wrap;
            justify-content: center !important;
            gap: 20px;
            margin-top: 15px;
        }

        p {
            color: $header-color;
            font-weight: 500;

            span {
                color: $theme-color;
            }
        }
        
        .social-icon {
            gap: 13px;

           a {
                width: 38px;
                height: 38px;
                line-height: 38px;
                text-align: center;
                background-color: $white;
                box-shadow: 0px 1px 8px 0px rgba(1, 16, 61, 0.07);
                border-radius: 50%;
                display: inline-block;
                color: #9497A3;

                &:hover {
                    background-color: #01103D;
                    color: $white;
                }
           }
        }
        
    }

   &.style-2 {
        border: none;
        padding: 0;
        padding-bottom: 30px;
        
        @include breakpoint (max-lg){
            text-align: center;
        }

        .footer-wrapper {
            position: relative;
            z-index: 9;
    
            p {
                color: $white;
    
                span {
                    color: $white;
                }

                a {
                    color: $white;
                }
            }

            .footer-menu {
                @include flex;
                gap: 20px;
    
                li {
                    a {
                        color: $white;
                        background-image: linear-gradient($white,$white);
                        background-position: 0 95%;
                        background-repeat: no-repeat;
                        background-size: 0% 2px;
                        display: inline-block;
                        @include transition;
    
                        &:hover {
                            background-size: 100% 1px;
                        }
                    }
                }
            }
        }

        .scroll-icon {
            background-color: $theme-color;
            color: $header-color;
    
            &:hover {
                background-color: $white;
            }
        }
   }
}

.footer-section {
    position: relative;

    .dot-shape {
        position: absolute;
        left: 7%;
        top: -5%;
    }

    .footer-shape-1 {
        position: absolute;
        left: 0;
        bottom: 10%;
    }

    .footer-shape-2 {
        position: absolute;
        top: 20%;
        right: 40px;
    }

    .line-shape {
        position: absolute;
        left: 0;
        top: 20%;
    }

    .star-shape {
        position: absolute;
        bottom: 110px;
        right: 70px;
    }

    &.style-2 {
        border-radius: 30px 30px 0px 0px;
    }
}

.footer-newsletter-wrapper {
    padding: 100px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.12);
    margin-bottom: -3px;
    @include flex;
    justify-content: space-between;

    @include breakpoint (max-xl){
        flex-wrap: wrap;
        gap: 30px;
    }

    @include breakpoint (max-lg){
       padding: 80px 0;
    }

    .newsletter-items {
        @include flex;
        gap: 40px;

        @include breakpoint (max-sm){
            gap: 20px;
            flex-wrap: wrap;
        }

        h2 {
            font-size: 37px;
            color: $white;
        }
    }

    .newsletter-input {
        max-width: 550px;
        position: relative;
        width: 100%;

        input {
            width: 100%;
            border-radius: 100px;
            background-color: $white;
            line-height: 1;
            padding: 20px 30px;
            border: none;
            color: #70737D;
            position: relative;

            &::placeholder {
                color: #70737D;
            }
        }

        .newsletter-btn {
            width: 50px;
            height: 50px;
            line-height: 50px;
            text-align: center;
            border-radius: 50%;
            background-color: $theme-color;
            color: $white;
            position: absolute;
            top: 5px;
            right: 5px;
            @include transition;

            i {
                transform: rotate(-40deg);
                @include transition;
            }

            &:hover {
                background-color: $header-color;

                i {
                    transform: rotate(0);
                }
            }
        }
    }

    &.style-2 {
        padding-top: 200px;
    }
}