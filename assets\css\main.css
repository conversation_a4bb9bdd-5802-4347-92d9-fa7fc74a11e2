/*
Theme Name: Prinoz
Author: Ui-Drops
Author URI: https://themeforest.net/user/ui-drops/portfolio
Description: Prinoz - Printing company & Service Html Template
Service Html Template 
Version: 1.0.0
*/
/*CSS Table Of Content Ends Here*/
@import url("https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300..700&display=swap");
@import url("https://fonts.cdnfonts.com/css/satoshi");
:root {
  --body: #fff;
  --black: #000;
  --white: #fff;
  --theme: #08bdc9;
  --theme2: #735cfc;
  --header: #0d1224;
  --text: #6e707a;
  --border: #d4dcff;
  --border-2: #d4dced;
  --bg: #f3f7fb;
  --bg2: #f6fbff;
  --box-shadow: 0px 1px 14px 0px rgba(0, 0, 0, 0.13);
}

.theme-btn {
  position: relative;
  z-index: 2;
  overflow: hidden;
  vertical-align: middle;
  display: inline-block;
  border: none;
  text-transform: capitalize;
  text-align: center;
  background-color: var(--black);
  color: var(--white);
  font-size: 16px;
  font-weight: 700;
  line-height: 1;
  padding: 21.5px 50px;
  min-width: 170px;
  border-radius: 50px;
}
.theme-btn i {
  margin-left: 5px;
}
.theme-btn:before {
  content: "";
  position: absolute;
  height: 100%;
  bottom: 0;
  left: 0;
  width: 0;
  background-color: var(--theme);
  z-index: -1;
  transition: all 0.4s ease-out;
  border-radius: inherit;
}
.theme-btn:hover {
  color: var(--white);
  box-shadow: none;
}
.theme-btn:hover::before {
  width: 100%;
}
@media (max-width: 767px) {
  .theme-btn {
    padding: 20px 32px;
  }
}
@media (max-width: 575px) {
  .theme-btn {
    padding: 18px 30px;
    font-size: 14px;
  }
}

.theme-btn-2 {
  font-size: 16px;
  display: inline-block;
  font-weight: 600;
  color: var(--text);
  text-transform: capitalize;
}
.theme-btn-2 i {
  margin-left: 10px;
}
.theme-btn-2:hover {
  color: var(--theme);
}

.link-btn {
  display: inline-block;
  color: var(--theme);
  text-align: center;
  font-family: "DM Sans";
  font-size: 18px;
  font-weight: 700;
  text-transform: capitalize;
}
.link-btn i {
  margin-left: 10px;
  transform: rotate(-40deg);
}

/* --------------------------------------------
    Template Default Fonts & Fonts Styles
 ---------------------------------------------- */
body {
  font-family: "Satoshi", sans-serif;
  font-size: 16px;
  font-weight: 500;
  line-height: 28px;
  color: var(--text);
  background-color: var(--white);
  padding: 0;
  margin: 0;
  overflow-x: hidden;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

button {
  border: none;
  background-color: transparent;
  padding: 0;
}

input:focus {
  color: var(--white);
  outline: none;
}

input {
  color: var(--white);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Space Grotesk", sans-serif;
  margin: 0px;
  padding: 0;
  color: var(--header);
  text-transform: unset;
  transition: all 0.4s ease-in-out;
}

h1 {
  font-size: 72px;
  font-weight: 700;
  line-height: 102%;
}

h2 {
  font-size: 50px;
  line-height: 110%;
  font-weight: 700;
}
@media (max-width: 1199px) {
  h2 {
    font-size: 42px;
  }
}
@media (max-width: 767px) {
  h2 {
    font-size: 44px;
  }
}
@media (max-width: 575px) {
  h2 {
    font-size: 38px;
  }
}

h3 {
  font-size: 22px;
  font-weight: 700;
}
@media (max-width: 575px) {
  h3 {
    font-size: 20px;
  }
}

h4 {
  font-size: 20px;
  font-weight: 700;
  line-height: 130%;
}

h5 {
  font-size: 18px;
  font-weight: 700;
}

h6 {
  font-size: 16px;
  font-weight: 700;
}

a {
  text-decoration: none;
  outline: none !important;
  cursor: pointer;
  color: var(--header);
  transition: all 0.4s ease-in-out;
}

p {
  margin: 0px;
  transition: all 0.4s ease-in-out;
}

span {
  margin: 0px;
  transition: all 0.4s ease-in-out;
}

@media (max-width: 1600px) {
  .about-wrapper {
    margin-bottom: 6px;
  }
}
@media (max-width: 1399px) {
  .about-wrapper {
    margin-bottom: 10px;
  }
}
@media (max-width: 991px) {
  .about-wrapper {
    margin-bottom: 6px;
  }
}
@media (max-width: 575px) {
  .about-wrapper {
    margin-bottom: 10px;
  }
}
.about-wrapper .about-image-items {
  position: relative;
}
@media (max-width: 991px) {
  .about-wrapper .about-image-items {
    text-align: center;
  }
}
.about-wrapper .about-image-items .about-image-1 {
  animation: translateX2 4s forwards infinite alternate;
}
.about-wrapper .about-image-items .about-image-1 img {
  max-width: 100%;
  border-radius: 20px;
}
.about-wrapper .about-image-items .about-image-2 {
  margin-top: 45px;
  position: relative;
  z-index: 9;
  animation: translateY2 4s forwards infinite alternate;
}
.about-wrapper .about-image-items .about-image-2 img {
  max-width: 100%;
  border-radius: 20px;
}
.about-wrapper .about-image-items .about-image-3 {
  position: absolute;
  top: 50%;
  right: 13%;
  transform: translateY(-50%);
}
.about-wrapper .about-image-items .about-image-3 img {
  max-width: 100%;
  border-radius: 20px;
}
.about-wrapper .about-image-items .circle-shape {
  position: absolute;
  top: 45px;
  left: -18%;
  z-index: -1;
}
@media (max-width: 1199px) {
  .about-wrapper .about-image-items .circle-shape {
    display: none;
  }
}
.about-wrapper .about-image-items .shape-img {
  position: absolute;
  bottom: -90px;
  right: 10px;
  z-index: -1;
}
@media (max-width: 1199px) {
  .about-wrapper .about-image-items .shape-img {
    display: none;
  }
}
.about-wrapper .about-image-items .cap-shape {
  position: absolute;
  bottom: 0;
  right: 10%;
}
@media (max-width: 1199px) {
  .about-wrapper .about-image-items .cap-shape {
    display: none;
  }
}
.about-wrapper .about-content p {
  font-size: 20px;
  font-weight: 600;
  line-height: 160%;
  margin-bottom: 50px;
}
.about-wrapper .about-content .icon-box-items {
  background-color: transparent;
  padding: 30px 25px;
  border-radius: 6px;
  transition: all 0.4s ease-in-out;
  border: 1px solid #e0e8e7;
}
.about-wrapper .about-content .icon-box-items .icon-items {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 10px;
}
@media (max-width: 1399px) {
  .about-wrapper .about-content .icon-box-items .icon-items {
    flex-wrap: wrap;
  }
}
.about-wrapper .about-content .icon-box-items .icon-items .icon {
  width: 54px;
  height: 54px;
  line-height: 50px;
  border-radius: 50%;
  text-align: center;
  font-size: 28px;
  color: var(--white);
  background-color: #58c09c;
}
.about-wrapper .about-content .icon-box-items .icon-items .icon.bg-2 {
  background-color: #f6d17c;
}
.about-wrapper .about-content .icon-box-items .icon-items h6 {
  font-family: "Satoshi", sans-serif;
}
.about-wrapper .about-content .icon-box-items span {
  font-weight: 400;
}
.about-wrapper .about-content .icon-box-items:hover,
.about-wrapper .about-content .icon-box-items.active {
  background-color: #f8faff;
  border: 1px solid var(--theme);
}
.about-wrapper .about-content .about-author {
  display: flex;
  align-items: center;
  gap: 30px;
  margin-top: 50px;
}
@media (max-width: 1399px) {
  .about-wrapper .about-content .about-author {
    gap: 30px;
  }
}
@media (max-width: 1399px) {
  .about-wrapper .about-content .about-author {
    flex-wrap: wrap;
  }
}
.about-wrapper .about-content .about-author .author-image {
  display: flex;
  align-items: center;
  gap: 10px;
}
.about-wrapper .about-content .about-author .author-image .content span {
  color: var(--text);
  font-weight: 700;
  margin-top: -10px;
  position: relative;
}
.about-wrapper .about-content .about-author .author-image .content span img {
  position: absolute;
  bottom: -10px;
  left: 4px;
}

.about-section {
  position: relative;
}
.about-section .dot-shape {
  position: absolute;
  top: 5%;
  left: 0;
}
@media (max-width: 1199px) {
  .about-section .dot-shape {
    display: none;
  }
}
.about-section .dot-shape-2 {
  position: absolute;
  top: 5%;
  right: 40px;
}
@media (max-width: 1199px) {
  .about-section .dot-shape-2 {
    display: none;
  }
}

.about-feature-section {
  margin: 0 60px;
  border-radius: 55px;
  margin-top: -3px;
  position: relative;
}
@media (max-width: 1600px) {
  .about-feature-section {
    margin: 0;
    border-radius: 0;
  }
}
.about-feature-section .product-shape {
  position: absolute;
  right: 0;
  bottom: 0;
}

.about-feature-wrapper {
  margin-bottom: 120px;
}
@media (max-width: 1199px) {
  .about-feature-wrapper {
    margin-bottom: 82px;
  }
}
.about-feature-wrapper .about-image-items .about-feature-image {
  max-width: 480px;
  position: relative;
}
@media (max-width: 991px) {
  .about-feature-wrapper .about-image-items .about-feature-image {
    max-width: 750px;
  }
}
.about-feature-wrapper .about-image-items .about-feature-image img {
  border-radius: 15px;
  width: 100%;
  height: 100%;
}
.about-feature-wrapper
  .about-image-items
  .about-feature-image
  .about-feature-image {
  max-width: 400px;
  position: absolute;
  right: -25%;
  bottom: -64%;
}
@media (max-width: 1199px) {
  .about-feature-wrapper
    .about-image-items
    .about-feature-image
    .about-feature-image {
    right: 0;
    bottom: 0;
    max-width: 300px;
  }
}
@media (max-width: 575px) {
  .about-feature-wrapper
    .about-image-items
    .about-feature-image
    .about-feature-image {
    max-width: 200px;
  }
}
.about-feature-wrapper .about-image-items .about-feature-image .stickers-shape {
  position: absolute;
  left: 0;
  bottom: -35%;
  z-index: 9;
  animation: rounded 5s linear infinite;
}
@media (max-width: 1199px) {
  .about-feature-wrapper
    .about-image-items
    .about-feature-image
    .stickers-shape {
    display: none;
  }
}
.about-feature-wrapper .about-feature-content {
  margin-left: 50px;
}
@media (max-width: 1199px) {
  .about-feature-wrapper .about-feature-content {
    margin-left: 25px;
  }
}
@media (max-width: 991px) {
  .about-feature-wrapper .about-feature-content {
    margin-left: 0;
  }
}
.about-feature-wrapper .about-feature-content .box-items-area .box-item {
  padding: 30px;
  margin-bottom: 10px;
  transition: all 0.4s ease-in-out;
  border-left: 5px solid transparent;
  max-width: 540px;
}
.about-feature-wrapper
  .about-feature-content
  .box-items-area
  .box-item:not(:last-child) {
  margin-bottom: 20px;
}
.about-feature-wrapper .about-feature-content .box-items-area .box-item h5 {
  margin-bottom: 10px;
}
.about-feature-wrapper .about-feature-content .box-items-area .box-item:hover,
.about-feature-wrapper .about-feature-content .box-items-area .box-item.active {
  background: var(--white);
  box-shadow: 0px 7px 22px 0px rgba(0, 0, 0, 0.06);
  border-radius: 14px;
  border-left: 5px solid var(--theme);
}
.about-feature-wrapper.style-2 {
  margin-bottom: -15px;
}
@media (max-width: 1199px) {
  .about-feature-wrapper.style-2 {
    margin-bottom: -45px;
  }
}

.about-wrapper-2 .about-image-items {
  position: relative;
}
.about-wrapper-2 .about-image-items .about-image {
  position: relative;
  text-align: center;
}
.about-wrapper-2 .about-image-items .about-image::before {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  content: "";
  width: 525px;
  height: 525px;
  border-radius: 50%;
  opacity: 0.2;
  background: linear-gradient(180deg, #6f32f0 0%, #ff47ee 100%);
}
@media (max-width: 1199px) {
  .about-wrapper-2 .about-image-items .about-image::before {
    width: 480px;
    height: 480px;
  }
}
@media (max-width: 991px) {
  .about-wrapper-2 .about-image-items .about-image::before {
    width: 525px;
    height: 525px;
  }
}
@media (max-width: 575px) {
  .about-wrapper-2 .about-image-items .about-image::before {
    width: 300px;
    height: 300px;
  }
}
@media (max-width: 575px) {
  .about-wrapper-2 .about-image-items .about-image img {
    width: 100%;
    height: 100%;
  }
}
.about-wrapper-2 .about-image-items .shape-1 {
  position: absolute;
  top: 10%;
  left: 0;
  animation: moving 9s linear infinite;
}
@media (max-width: 470px) {
  .about-wrapper-2 .about-image-items .shape-1 {
    display: none;
  }
}
.about-wrapper-2 .about-image-items .shape-2 {
  position: absolute;
  bottom: 0;
  left: 0;
  animation: translateX2 2s forwards infinite alternate;
}
.about-wrapper-2 .about-image-items .shape-3 {
  position: absolute;
  top: 60px;
  right: 70px;
  animation: translateX2 2s forwards infinite alternate;
}
.about-wrapper-2 .about-image-items .shape-4 {
  position: absolute;
  bottom: 12px;
  right: 20px;
  animation: translateY2 2s forwards infinite alternate;
}
@media (max-width: 470px) {
  .about-wrapper-2 .about-image-items .shape-4 {
    display: none;
  }
}
.about-wrapper-2 .about-image-items .shape-5 {
  position: absolute;
  bottom: 35%;
  right: 30px;
}
.about-wrapper-2 .about-content p {
  padding-top: 10px;
  font-size: 20px;
  font-weight: 400;
}
.about-wrapper-2 .about-content .about-list {
  margin-top: 40px;
}
@media (max-width: 575px) {
  .about-wrapper-2 .about-content .about-list {
    margin-top: 30px;
  }
}
.about-wrapper-2 .about-content .about-list li {
  font-size: 18px;
  font-weight: 400;
}
@media (max-width: 575px) {
  .about-wrapper-2 .about-content .about-list li {
    font-size: 16px;
  }
}
.about-wrapper-2 .about-content .about-list li:not(:last-child) {
  margin-bottom: 20px;
}
.about-wrapper-2 .about-content .about-list li i {
  color: var(--theme2);
  margin-right: 10px;
}
.about-wrapper-2 .about-content .about-author {
  display: flex;
  align-items: center;
  gap: 30px;
  margin-top: 55px;
}
@media (max-width: 575px) {
  .about-wrapper-2 .about-content .about-author {
    margin-top: 30px;
  }
}
@media (max-width: 1399px) {
  .about-wrapper-2 .about-content .about-author {
    gap: 40px;
  }
}
@media (max-width: 1199px) {
  .about-wrapper-2 .about-content .about-author {
    flex-wrap: wrap;
  }
}
.about-wrapper-2 .about-content .about-author .author-image {
  display: flex;
  align-items: center;
  gap: 10px;
}
.about-wrapper-2 .about-content .about-author .author-image .content span {
  color: var(--text);
  font-weight: 700;
  margin-top: -10px;
  position: relative;
}
.about-wrapper-2 .about-content .about-author .author-image .content span img {
  position: absolute;
  bottom: -10px;
  left: 4px;
}
.about-wrapper-2.style-2 {
  margin-bottom: 75px;
}
.about-wrapper-2.style-2 .about-feature-image-2 {
  margin-bottom: -130px;
  position: relative;
  z-index: 9;
  margin-left: -180px;
}
@media (max-width: 1399px) {
  .about-wrapper-2.style-2 .about-feature-image-2 {
    margin-left: 0;
  }
}
@media (max-width: 991px) {
  .about-wrapper-2.style-2 .about-feature-image-2 {
    margin-bottom: -80px;
  }
}
.about-wrapper-2.style-2 .about-feature-image-2 .bg-shape {
  position: absolute;
  top: 50%;
  left: 45%;
  transform: translate(-50%, -50%);
  z-index: -1;
}
@media (max-width: 1199px) {
  .about-wrapper-2.style-2 .about-feature-image-2 .bg-shape {
    display: none;
  }
}
.about-wrapper-2.style-2 .about-feature-image-2 .content-box {
  position: absolute;
  top: -130px;
  right: -18%;
}
@media (max-width: 1899px) {
  .about-wrapper-2.style-2 .about-feature-image-2 .content-box {
    display: none;
  }
}
@media (max-width: 1199px) {
  .about-wrapper-2.style-2 .about-feature-image-2 img {
    width: 100%;
    height: 100%;
  }
}

@-webkit-keyframes rippleOne {
  70% {
    -webkit-box-shadow: 0 0 0 40px rgba(244, 68, 56, 0);
    box-shadow: 0 0 0 40px rgba(244, 68, 56, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(244, 68, 56, 0);
    box-shadow: 0 0 0 0 rgba(244, 68, 56, 0);
  }
}
@keyframes rippleOne {
  70% {
    -webkit-box-shadow: 0 0 0 40px rgba(244, 68, 56, 0);
    box-shadow: 0 0 0 40px rgba(244, 68, 56, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(244, 68, 56, 0);
    box-shadow: 0 0 0 0 rgba(244, 68, 56, 0);
  }
}
@keyframes cir36 {
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rounded {
  50% {
    transform: rotate(15deg);
  }
}
@keyframes up-down {
  0% {
    transform: translateY(10px);
  }
  100% {
    transform: translateY(-10px);
  }
}
@-webkit-keyframes spinner {
  to {
    -webkit-transform: rotateZ(360deg);
    transform: rotateZ(360deg);
  }
}
@keyframes spinner {
  to {
    -webkit-transform: rotateZ(360deg);
    transform: rotateZ(360deg);
  }
}
@-webkit-keyframes letters-loading {
  0%,
  75%,
  100% {
    opacity: 0;
    transform: rotateY(-90deg);
  }
  25%,
  50% {
    opacity: 1;
    transform: rotateY(0deg);
  }
}
@keyframes letters-loading {
  0%,
  75%,
  100% {
    opacity: 0;
    transform: rotateY(-90deg);
  }
  25%,
  50% {
    opacity: 1;
    transform: rotateY(0deg);
  }
}
@keyframes loaderspin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
@keyframes tpswing {
  0% {
    -webkit-transform: rotate(20deg);
    -ms-transform: rotate(20deg);
    transform: rotate(20deg);
  }
  100% {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
}
@keyframes width {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes width {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes loaderspin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
@keyframes loaderpulse {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.2);
  }
}
@keyframes rounded {
  50% {
    transform: rotate(20deg);
  }
}
@keyframes cir36 {
  100% {
    transform: rotate(360deg);
  }
}
.float-bob-y {
  -webkit-animation-name: float-bob-y;
  animation-name: float-bob-y;
  -webkit-animation-duration: 3s;
  animation-duration: 3s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

@-webkit-keyframes float-bob-y {
  0% {
    -webkit-transform: translateY(-30px);
    transform: translateY(-30px);
  }
  50% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(-30px);
    transform: translateY(-30px);
  }
}
@keyframes float-bob-y {
  0% {
    -webkit-transform: translateY(-30px);
    transform: translateY(-30px);
  }
  50% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(-30px);
    transform: translateY(-30px);
  }
}
.float-bob-x {
  -webkit-animation-name: float-bob-x;
  animation-name: float-bob-x;
  -webkit-animation-duration: 3s;
  animation-duration: 3s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

@-webkit-keyframes float-bob-x {
  0% {
    -webkit-transform: translateX(0px);
    transform: translateX(30px);
  }
  50% {
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }
  100% {
    -webkit-transform: translateX(30px);
    transform: translateX(30px);
  }
}
@keyframes float-bob-x {
  0% {
    -webkit-transform: translateX(30px);
    transform: translateX(30px);
  }
  50% {
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }
  100% {
    -webkit-transform: translateX(30px);
    transform: translateX(30px);
  }
}
@keyframes bounce-x {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  50% {
    -webkit-transform: translateX(30px);
    transform: translateX(30px);
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
.bounce-x {
  -webkit-animation: bounce-x 7s infinite linear;
  animation: bounce-x 7s infinite linear;
}

@keyframes criss-cross-left {
  0% {
    left: -20px;
  }
  50% {
    left: 50%;
    width: 20px;
    height: 20px;
  }
  100% {
    left: 50%;
    width: 375px;
    height: 375px;
  }
}
@keyframes criss-cross-right {
  0% {
    right: -20px;
  }
  50% {
    right: 50%;
    width: 20px;
    height: 20px;
  }
  100% {
    right: 50%;
    width: 375px;
    height: 375px;
  }
}
@keyframes rotated2 {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(-360deg);
  }
}
@keyframes wave {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-25%);
  }
  100% {
    transform: translateX(-50%);
  }
}
@keyframes zoom {
  0% {
    transform: scale(0.5);
  }
  50% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.5);
  }
}
@keyframes translateY2 {
  0% {
    -webkit-transform: translateY(-30px);
    -moz-transform: translateY(-30px);
    -ms-transform: translateY(-30px);
    -o-transform: translateY(-30px);
    transform: translateY(-30px);
  }
  100% {
    -webkit-transform: translateY(20px);
    -moz-transform: translateY(20px);
    -ms-transform: translateY(20px);
    -o-transform: translateY(20px);
    transform: translateY(20px);
  }
}
@keyframes translateX2 {
  0% {
    -webkit-transform: translateX(-30px);
    -moz-transform: translateX(-30px);
    -ms-transform: translateX(-30px);
    -o-transform: translateX(-30px);
    transform: translateX(-30px);
  }
  100% {
    -webkit-transform: translatXY(20px);
    -moz-transform: translateX(20px);
    -ms-transform: translateX(20px);
    -o-transform: translateX(20px);
    transform: translateX(20px);
  }
}
@keyframes moving {
  0% {
    transform: translatey(0px);
  }
  20% {
    transform: translateX(-50px);
  }
  50% {
    transform: translatey(-40px);
  }
  100% {
    transform: translatey(0px);
  }
}
.img-custom-anim-top {
  animation: img-anim-top 1.3s forwards cubic-bezier(0.645, 0.045, 0.355, 1);
  opacity: 0;
}

@keyframes img-anim-top {
  0% {
    transform: translateY(-5%);
    clip-path: inset(0 0 100% 0);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    clip-path: inset(0 0 0 0);
    opacity: 1;
  }
}
.brand-wrapper {
  background-color: var(--white);
  border: 1px solid rgba(124, 138, 166, 0.15);
  box-shadow: 0px 10px 16px 0px rgba(0, 0, 0, 0.04);
  padding: 60px 55px 25px;
  margin-top: -75px;
  position: relative;
  z-index: 9;
}
.brand-wrapper .brand-logo {
  text-align: center;
  padding: 30px 0;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.06);
  margin-bottom: 35px;
  margin-top: 5px;
}
.brand-wrapper .brand-logo.style-2 {
  margin-top: 0;
}

.brand-wrapper-3 {
  padding: 45px 0;
  border-bottom: 1px dashed #e2dfe3;
}
.brand-wrapper-3 .brand-image {
  text-align: center;
}

.brand-wrapper-4 {
  margin: 0 55px;
  border-radius: 0 0 25px 25px;
  height: 715px;
  padding-top: 362px;
}
@media (max-width: 1399px) {
  .brand-wrapper-4 {
    margin: 0 20px;
  }
}
@media (max-width: 767px) {
  .brand-wrapper-4 {
    padding-top: 310px;
  }
}
.brand-wrapper-4 h4 {
  margin-top: 50px;
}
.brand-wrapper-4 h4 span {
  color: #ff1b1b;
}
.brand-wrapper-4 .brand-items {
  border: 1px dashed #252525;
  padding: 0 25px;
  margin-top: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 50px;
  position: relative;
}
@media (max-width: 1199px) {
  .brand-wrapper-4 .brand-items {
    flex-wrap: wrap;
    gap: 30px;
    justify-content: start;
    border: none;
  }
}
@media (max-width: 767px) {
  .brand-wrapper-4 .brand-items {
    margin-top: 20px;
  }
}
.brand-wrapper-4 .brand-items .brand-logo {
  padding: 45px 0;
}
@media (max-width: 1199px) {
  .brand-wrapper-4 .brand-items .brand-logo {
    padding: 0;
  }
}
.brand-wrapper-4 .brand-items .brand-logo:not(:last-child) {
  border-right: 1px dashed #252525;
  padding-right: 50px;
}
@media (max-width: 1199px) {
  .brand-wrapper-4 .brand-items .brand-logo:not(:last-child) {
    border: none;
    padding-right: 0;
  }
}

.contact-info-items {
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: 0px 15px 10px 0px rgba(242, 242, 242, 0.18);
  padding: 60px 30px;
  transition: all 0.4s ease-in-out;
  position: relative;
  z-index: 1;
}
.contact-info-items .icon {
  transition: all 0.4s ease-in-out;
}
.contact-info-items .content {
  margin-top: 20px;
}
.contact-info-items .content h3 {
  font-size: 24px;
  margin-bottom: 5px;
}
.contact-info-items:hover,
.contact-info-items.active {
  background-color: #600ee4;
}
.contact-info-items:hover::before,
.contact-info-items.active::before {
  opacity: 1;
  visibility: visible;
}
.contact-info-items:hover .icon,
.contact-info-items.active .icon {
  filter: brightness(0) saturate(100%) invert(100%) sepia(3%) saturate(2%)
    hue-rotate(183deg) brightness(107%) contrast(101%);
}
.contact-info-items:hover .content h3,
.contact-info-items:hover .content p,
.contact-info-items.active .content h3,
.contact-info-items.active .content p {
  color: var(--white);
}

.contact-area .map-content-area h3 {
  margin-bottom: 15px;
  margin-top: -10px;
  font-size: 44px;
}
.contact-area .map-content-area .google-map {
  margin-top: 50px;
}
.contact-area .map-content-area .google-map iframe {
  width: 100%;
  height: 350px;
}
.contact-area .contact-form-items .contact-title {
  margin-bottom: 30px;
}
.contact-area .contact-form-items .contact-title h3 {
  margin-top: -10px;
  font-size: 44px;
  margin-bottom: 10px;
}
.contact-area .contact-form-items .form-clt {
  position: relative;
}
.contact-area .contact-form-items .form-clt .icon {
  position: absolute;
  top: 18px;
  left: 0;
}
@media (max-width: 575px) {
  .contact-area .contact-form-items .form-clt .icon {
    display: none;
  }
}
.contact-area .contact-form-items .form-clt .icon i {
  color: var(--header);
}
.contact-area .contact-form-items .form-clt input {
  width: 100%;
  outline: none;
  border: none;
  padding: 20px 30px;
  color: var(--text);
  background-color: transparent;
  text-transform: capitalize;
  font-weight: 500;
  border-bottom: 1px solid var(--text);
}
@media (max-width: 575px) {
  .contact-area .contact-form-items .form-clt input {
    padding: 15px 20px;
  }
}
.contact-area .contact-form-items .form-clt-big textarea {
  padding: 20px 30px 110px;
  width: 100%;
  outline: none;
  border: none;
  color: var(--text);
  background-color: transparent;
  text-transform: capitalize;
  font-weight: 500;
  border-bottom: 1px solid var(--text);
}
.contact-area .contact-form-items .theme-btn {
  border-radius: 0;
  background-color: #600ee4;
}
.contact-area .contact-form-items .theme-btn i {
  margin-left: 0;
  margin-right: 10px;
}
.contact-area .contact-form-items .theme-btn::before {
  background-color: var(--header);
}

.cta-wrapper {
  padding: 70px 130px;
  border-radius: 30px;
  position: relative;
  margin-top: -130px;
  z-index: 9;
  margin-bottom: -5px;
}
@media (max-width: 991px) {
  .cta-wrapper {
    padding: 60px 50px;
  }
}
@media (max-width: 767px) {
  .cta-wrapper {
    padding: 50px 40px;
  }
}
.cta-wrapper .cta-image {
  position: absolute;
  right: 0;
  top: 0;
}
@media (max-width: 767px) {
  .cta-wrapper .cta-image {
    display: none;
  }
}
.cta-wrapper .hand-bag {
  position: absolute;
  top: 0;
  left: 62%;
  transform: translateX(-50%);
}
.cta-wrapper .t-shirt-shape {
  position: absolute;
  bottom: 0;
  left: 62%;
  transform: translateX(-50%);
}
.cta-wrapper .prite-box {
  position: absolute;
  bottom: 30px;
  left: 10px;
}

.cta-wrapper-2 {
  padding: 60px 150px;
  border-radius: 30px;
  position: relative;
  margin-bottom: -100px;
  position: relative;
  z-index: 9;
}
@media (max-width: 991px) {
  .cta-wrapper-2 {
    padding: 60px 50px;
  }
}
@media (max-width: 767px) {
  .cta-wrapper-2 {
    padding: 50px 40px;
  }
}
.cta-wrapper-2 .cta-image {
  position: absolute;
  top: 25px;
  right: 65px;
}
@media (max-width: 767px) {
  .cta-wrapper-2 .cta-image {
    display: none;
  }
}
.cta-wrapper-2 .hand-bag {
  position: absolute;
  top: 0;
  left: 62%;
  transform: translateX(-50%);
}
.cta-wrapper-2 .prite-box {
  position: absolute;
  bottom: 30px;
  left: 10px;
}
.cta-wrapper-2 .cta-sticker {
  position: absolute;
  bottom: 15px;
  left: 65%;
  transform: translatex(-50%);
}

.cta-section-3 {
  position: relative;
}
.cta-section-3 .mycustom-marque.style-3 {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
}

.cta-video-section {
  position: relative;
}
.cta-video-section .container-fluid {
  padding: 0 260px;
}
@media (max-width: 1399px) {
  .cta-video-section .container-fluid {
    padding: 0 20px;
  }
}

.cta-video-wrapper {
  padding: 200px 0;
  border-radius: 21px;
  position: relative;
  height: 586px;
  margin-bottom: -320px;
  z-index: 9;
}
@media (max-width: 767px) {
  .cta-video-wrapper {
    height: 480px;
  }
}
@media (max-width: 575px) {
  .cta-video-wrapper {
    height: 400px;
  }
}
.cta-video-wrapper .video-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  display: block;
  width: 180px;
  height: 180px;
  line-height: 180px;
  background-color: #600ee4;
  color: var(--white);
  text-align: center;
  font-size: 40px;
}
@media (max-width: 767px) {
  .cta-video-wrapper .video-btn {
    width: 160px;
    height: 160px;
    line-height: 160px;
  }
}
@media (max-width: 575px) {
  .cta-video-wrapper .video-btn {
    width: 130px;
    height: 130px;
    line-height: 130px;
    font-size: 24px;
  }
}

.faq-wrapper .faq-content .accordion-item {
  border: 0;
  margin-top: 20px;
  background: transparent;
  border: 1px solid #ccd5e8;
}
.faq-wrapper .faq-content .accordion-item .accordion-header {
  background-color: transparent;
}
.faq-wrapper .faq-content .accordion-item .accordion-header .accordion-button {
  font-weight: 700;
  color: var(--header);
  border: 0;
  border-radius: 8px;
  box-shadow: none;
  background-color: var(--white);
  padding: 26px 40px;
  font-size: 20px;
}
.faq-wrapper
  .faq-content
  .accordion-item
  .accordion-header
  .accordion-button::after {
  content: "\f063";
  background: transparent;
  font-family: "Font Awesome 6 Pro";
  font-weight: 300;
  transition: all 0.3s ease-in-out !important;
}
.faq-wrapper
  .faq-content
  .accordion-item
  .accordion-header
  .accordion-button:not(.collapsed)::after {
  content: "\f062";
  background: transparent;
  font-family: "Font Awesome 6 Pro";
  font-weight: 300;
  color: var(--theme);
}
.faq-wrapper
  .faq-content
  .accordion-item
  .accordion-header
  .accordion-button.collapsed {
  background-color: transparent;
}
.faq-wrapper .faq-content .accordion-item .accordion-collapse {
  background-color: var(--white);
  border: 1px solid var(--white);
}
.faq-wrapper .faq-content .accordion-item .accordion-collapse .accordion-body {
  padding-right: 115px;
  margin-top: -25px;
  padding-left: 40px;
  margin-bottom: 5px;
  font-size: 18px;
  color: var(--header);
  opacity: 0.8;
  font-weight: 400;
}
.faq-wrapper.style-2 .faq-content .accordion-item {
  border: 1px solid rgba(4, 18, 31, 0.08);
  background: var(--white);
}
.faq-wrapper.style-2
  .faq-content
  .accordion-item
  .accordion-header
  .accordion-button::after {
  content: "\f078";
}
.faq-wrapper.style-2
  .faq-content
  .accordion-item
  .accordion-header
  .accordion-button:not(.collapsed)::after {
  content: "\f077";
}
.faq-wrapper.style-2
  .faq-content
  .accordion-item
  .accordion-header
  .accordion-button.collapsed {
  background-color: transparent;
  border: 1px solid var(--white);
}
.faq-wrapper.style-2 .faq-content .accordion-item .accordion-collapse {
  background-color: var(--white);
  box-shadow: 0px 14px 50px 0px rgba(4, 11, 17, 0.12);
  border: 1px solid var(--white);
}
.faq-wrapper.style-2
  .faq-content
  .accordion-item
  .accordion-collapse
  .accordion-body {
  padding-right: 115px;
  margin-top: -25px;
  padding-left: 40px;
  margin-bottom: 10px;
  font-size: 18px;
  color: var(--text);
  opacity: 0.8;
  font-weight: 400;
}

.faq-section {
  margin: 0 50px;
  border-radius: 25px;
}
@media (max-width: 1399px) {
  .faq-section {
    margin: 0;
    border-radius: 0;
  }
}

.feature-wrapper {
  border: 1px dashed #c8cbcd;
  border-radius: 15px;
  padding: 60px 30px;
}
.feature-wrapper .feature-box-items {
  display: flex;
  gap: 35px;
}
@media (max-width: 470px) {
  .feature-wrapper .feature-box-items {
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
    text-align: center;
  }
}
.feature-wrapper .feature-box-items .content p {
  font-weight: 400;
  margin-top: 10px;
}

.feature-wrapper-2 {
  background-color: var(--white);
  border-radius: 30px;
  box-shadow: 0px 19px 33px 0px rgba(0, 0, 0, 0.04);
  padding: 80px 35px;
  margin-top: -70px;
  position: relative;
  z-index: 9;
}
@media (max-width: 1399px) {
  .feature-wrapper-2 {
    margin-top: -78px;
  }
}
@media (max-width: 767px) {
  .feature-wrapper-2 {
    margin-top: -75px;
  }
}
@media (max-width: 767px) {
  .feature-wrapper-2 {
    margin-top: -75px;
  }
}
.feature-wrapper-2 .feature-box-items-2 .icon {
  width: 100px;
  height: 100px;
  text-align: center;
  line-height: 100px;
  text-align: center;
  background-color: #fff0e2;
  border-radius: 50%;
  margin: 0 auto 25px;
}
.feature-wrapper-2 .feature-box-items-2 .icon.bg-2 {
  background-color: #deefff;
}
.feature-wrapper-2 .feature-box-items-2 .icon.bg-3 {
  background-color: #e5ffe7;
}
.feature-wrapper-2 .feature-box-items-2 .icon.bg-4 {
  background-color: #d7e9fa;
}
.feature-wrapper-2 .feature-box-items-2 .content h3 {
  margin-bottom: 10px;
}
.feature-wrapper-2 .feature-box-items-2 .content p {
  font-weight: 400;
}

.counter-items .counter-title {
  padding: 25px 30px;
  text-align: center;
  border-radius: 100px;
  background: linear-gradient(
    145.27deg,
    rgba(255, 2, 154, 0.17) 15.55%,
    rgba(159, 114, 144, 0.0238) 86.81%
  );
  margin-bottom: 20px;
  margin-top: 30px;
}
.counter-items .counter-title.bg-2 {
  background: linear-gradient(
    145deg,
    rgba(255, 74, 17, 0.14) 15.55%,
    rgba(248, 206, 191, 0.14) 86.81%
  );
}
.counter-items .counter-title.bg-3 {
  background: linear-gradient(
    145deg,
    rgba(0, 236, 9, 0.14) 15.55%,
    rgba(29, 227, 73, 0) 86.81%
  );
}
.counter-items .counter-title.bg-4 {
  background: linear-gradient(
    145deg,
    rgba(145, 5, 255, 0.14) 15.55%,
    rgba(185, 173, 255, 0.03) 86.81%
  );
}
.counter-items .counter-title h2 {
  font-size: 50px;
  font-weight: 700;
  color: var(--header);
  line-height: 1;
}
.counter-items p {
  font-size: 16px;
  font-weight: 700;
  color: var(--header);
  font-family: "Space Grotesk", sans-serif;
}

.counter-text {
  margin-bottom: 30px;
}
@media (max-width: 767px) {
  .counter-text {
    margin-bottom: 0;
  }
}
.counter-text h6 {
  font-size: 20px;
  font-size: "Satoshi", sans-serif;
  line-height: 1;
}

.fearure-wrapper-3 {
  background-color: var(--white);
  box-shadow: var(--box-shadow);
  padding: 70px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: -130px;
  position: relative;
  z-index: 9;
}
@media (max-width: 1199px) {
  .fearure-wrapper-3 {
    flex-wrap: wrap;
    gap: 30px;
  }
}
@media (max-width: 575px) {
  .fearure-wrapper-3 {
    justify-content: center;
    text-align: center;
  }
}
.fearure-wrapper-3 .feature-item {
  text-align: center;
  position: relative;
  z-index: 9;
}
.fearure-wrapper-3 .feature-item .feature-content {
  margin-top: 20px;
}
.fearure-wrapper-3 .feature-item .feature-content h5 {
  font-weight: 500;
  line-height: 150%;
  font-family: "Satoshi", sans-serif;
}
.fearure-wrapper-3 .line-shape {
  position: absolute;
  top: 38%;
  left: 50%;
  transform: translate(-50%, -50%);
}
@media (max-width: 1199px) {
  .fearure-wrapper-3 .line-shape {
    display: none;
  }
}

.feature-section-3 {
  position: relative;
}
.feature-section-3 .bg-line-shape {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}
.feature-section-3 .bg-line-shape img {
  width: 100%;
  height: 100%;
}

.feature-icon-box-section {
  padding: 0 128px;
}
@media (max-width: 1600px) {
  .feature-icon-box-section {
    padding: 0 30px;
  }
}

.feature-box-wrapper {
  padding: 30px 0;
  border-bottom: 1px solid #e1e1e1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 1600px) {
  .feature-box-wrapper {
    flex-wrap: wrap;
    gap: 30px;
  }
}
.feature-box-wrapper .icon-box-items {
  display: flex;
  align-items: center;
  gap: 18px;
}
.feature-box-wrapper .icon-box-items:not(:last-child) {
  border-right: 1px solid #d9d9d9;
  padding-right: 50px;
}
@media (max-width: 1600px) {
  .feature-box-wrapper .icon-box-items:not(:last-child) {
    padding-right: 0;
    border: none;
  }
}
.feature-box-wrapper .icon-box-items p {
  font-size: 16px;
  font-weight: 700;
  color: var(--header);
  text-transform: capitalize;
}

.work-process-wrapper {
  margin-bottom: 20px;
}

.work-process-box-items {
  margin-top: 30px;
  padding: 50px;
  text-align: center;
  border-radius: 15px;
  border: 1px dashed #ccc6c6;
  position: relative;
  transition: all 0.4s ease-in-out;
  background: transparent;
}
.work-process-box-items .thumb {
  margin-bottom: 25px;
}
.work-process-box-items .thumb img {
  border-radius: 51px;
}
.work-process-box-items .content h3 {
  font-size: 26px;
  margin-bottom: 20px;
}
.work-process-box-items .content p {
  font-size: 18px;
  margin-bottom: 20px;
  font-weight: 400;
}
.work-process-box-items .number {
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background-color: var(--theme);
  color: var(--white);
  font-size: 18px;
  font-weight: 700;
  font-family: "Satoshi", sans-serif;
  border-radius: 50%;
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
}
.work-process-box-items:hover,
.work-process-box-items.active {
  background: #f9f3fe;
}

.feature-wrapper-3 {
  margin-bottom: 25px;
}
@media (max-width: 1199px) {
  .feature-wrapper-3 {
    margin-bottom: 0;
  }
}
.feature-wrapper-3 .feature-image {
  position: relative;
  z-index: 9;
}
@media (max-width: 1199px) {
  .feature-wrapper-3 .feature-image img {
    width: 100%;
  }
}
.feature-wrapper-3 .feature-image::after {
  position: absolute;
  top: 35%;
  left: 35%;
  transform: translate(-50%, -50%);
  content: "";
  width: 500px;
  height: 500px;
  border-radius: 50%;
  background: linear-gradient(
    76deg,
    #ffc83a 13.64%,
    #ff008a 46.53%,
    #6100ff 78.88%
  );
  z-index: -1;
  opacity: 0.08;
  filter: blur(32px);
  z-index: -1;
}
.feature-wrapper-3 .feature-image::before {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  content: "";
  width: 500px;
  height: 500px;
  border-radius: 50%;
  background: #deefc8;
  z-index: -1;
}
@media (max-width: 575px) {
  .feature-wrapper-3 .feature-image::before {
    width: 400px;
    height: 400px;
  }
}
@media (max-width: 470px) {
  .feature-wrapper-3 .feature-image::before {
    width: 350px;
    height: 350px;
  }
}
.feature-wrapper-3 .feature-image .customer-shape {
  position: absolute;
  bottom: -30px;
  right: 0;
}
@media (max-width: 575px) {
  .feature-wrapper-3 .feature-image .customer-shape {
    display: none;
  }
}
.feature-wrapper-3 .feature-image .line-shape {
  position: absolute;
  top: 80px;
  left: -70px;
  z-index: -11;
}
@media (max-width: 1399px) {
  .feature-wrapper-3 .feature-image .line-shape {
    display: none;
  }
}
.feature-wrapper-3 .feature-image .color-cycle {
  position: absolute;
  top: 40px;
  left: 60px;
  animation: translateX2 2s forwards infinite alternate;
}
.feature-wrapper-3 .feature-image .box-shape {
  position: absolute;
  top: 30%;
  left: 40px;
  animation: moving 9s linear infinite;
}
.feature-wrapper-3 .feature-image .stickers-shape {
  position: absolute;
  bottom: 0px;
  left: 30px;
  animation: rounded 5s linear infinite;
}
@media (max-width: 575px) {
  .feature-wrapper-3 .feature-image .stickers-shape {
    display: none;
  }
}
.feature-wrapper-3 .feature-image .shirt-shape {
  position: absolute;
  right: 10px;
  top: 110px;
  animation: translateY2 2s forwards infinite alternate;
}
.feature-wrapper-3 .feature-content {
  margin-left: 40px;
}
@media (max-width: 991px) {
  .feature-wrapper-3 .feature-content {
    margin-left: 0;
  }
}
.feature-wrapper-3 .feature-content p {
  font-size: 20px;
  font-weight: 400;
}
@media (max-width: 767px) {
  .feature-wrapper-3 .feature-content p {
    font-size: 18px;
  }
}
@media (max-width: 575px) {
  .feature-wrapper-3 .feature-content p {
    font-size: 16px;
  }
}
.feature-wrapper-3 .feature-content .list-items {
  margin-top: 40px;
  margin-bottom: 50px;
}
@media (max-width: 767px) {
  .feature-wrapper-3 .feature-content .list-items {
    margin-bottom: 30px;
    margin-top: 30px;
  }
}
.feature-wrapper-3 .feature-content .list-items li {
  font-size: 16px;
  font-weight: 400;
  color: #696969;
  text-transform: capitalize;
}
.feature-wrapper-3 .feature-content .list-items li:not(:last-child) {
  margin-bottom: 15px;
}
.feature-wrapper-3 .feature-content .list-items li i {
  width: 33px;
  height: 33px;
  line-height: 33px;
  border-radius: 50%;
  text-align: center;
  background-color: #eee5ff;
  color: #6f32f0;
  margin-right: 10px;
}
.feature-wrapper-3 .feature-content .theme-btn {
  font-family: "Space Grotesk", sans-serif;
}

.footer-widgets-wrapper {
  padding: 100px 0 123px;
  position: relative;
  z-index: 9;
}
@media (max-width: 1199px) {
  .footer-widgets-wrapper {
    padding: 60px 0 90px;
  }
}
@media (max-width: 991px) {
  .footer-widgets-wrapper {
    padding: 50px 0 70px;
  }
}
.footer-widgets-wrapper .single-footer-widget {
  margin-top: 30px;
}
.footer-widgets-wrapper .single-footer-widget .widget-head {
  margin-bottom: 30px;
}
@media (max-width: 575px) {
  .footer-widgets-wrapper .single-footer-widget .widget-head {
    margin-bottom: 20px;
  }
}
.footer-widgets-wrapper .single-footer-widget .widget-head h3 {
  font-weight: bold;
  color: var(--header);
  font-size: 22px;
  display: inline-block;
}
.footer-widgets-wrapper .single-footer-widget .footer-content p {
  color: var(--header);
}
.footer-widgets-wrapper .single-footer-widget .footer-content .contact-list {
  margin-top: 30px;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .contact-list li {
  font-size: 18px;
}
.footer-widgets-wrapper
  .single-footer-widget
  .footer-content
  .contact-list
  li:not(:last-child) {
  margin-bottom: 15px;
}
.footer-widgets-wrapper
  .single-footer-widget
  .footer-content
  .contact-list
  li
  i {
  color: var(--theme);
  margin-right: 10px;
}
.footer-widgets-wrapper
  .single-footer-widget
  .footer-content
  .contact-list
  li
  a {
  color: #4f5055;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .scan-items {
  display: flex;
  align-items: center;
  gap: 13px;
  flex-wrap: wrap;
}
.footer-widgets-wrapper
  .single-footer-widget
  .footer-content
  .scan-items
  .scan-img {
  background-color: var(--white);
  border-radius: 10px;
  box-shadow: 0px 1px 17px 0px rgba(1, 16, 61, 0.09);
  display: inline-block;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.footer-widgets-wrapper
  .single-footer-widget
  .footer-content
  .scan-items
  .store-list
  li {
  font-size: 14px;
  font-weight: 700;
  font-family: "Space Grotesk", sans-serif;
}
.footer-widgets-wrapper
  .single-footer-widget
  .footer-content
  .scan-items
  .store-list
  li:not(:last-child) {
  margin-bottom: 16px;
}
.footer-widgets-wrapper
  .single-footer-widget
  .footer-content
  .scan-items
  .store-list
  li
  a {
  display: flex;
  align-items: center;
  color: var(--white);
  padding: 13px 35px;
  border-radius: 5px;
  background: #01103d;
  box-shadow: 0px 1px 2px 0px rgba(1, 16, 61, 0.14);
  gap: 10px;
  line-height: 1;
}
.footer-widgets-wrapper
  .single-footer-widget
  .footer-content
  .scan-items
  .store-list
  li.active
  a {
  color: #01103d;
  background: var(--white);
}
.footer-widgets-wrapper .single-footer-widget .footer-content .brand-logo {
  margin-top: 30px;
}
.footer-widgets-wrapper .single-footer-widget .footer-content .brand-logo img {
  max-width: 100%;
}
.footer-widgets-wrapper .single-footer-widget .list-items li {
  transition: all 0.4s ease-in-out;
  font-weight: 500;
}
.footer-widgets-wrapper .single-footer-widget .list-items li:not(:last-child) {
  margin-bottom: 15px;
}
.footer-widgets-wrapper .single-footer-widget .list-items li a {
  color: var(--header);
  font-weight: 500;
}
.footer-widgets-wrapper .single-footer-widget .list-items li:hover {
  margin-left: 5px;
}
.footer-widgets-wrapper .single-footer-widget .list-items li:hover a {
  color: var(--theme2);
}
.footer-widgets-wrapper.style-2 .widget-head h3 {
  color: var(--white);
}
.footer-widgets-wrapper.style-2 .footer-content p {
  font-weight: 400;
}
.footer-widgets-wrapper.style-2 .footer-content .contact-list {
  margin-top: 15px;
}
.footer-widgets-wrapper.style-2 .footer-content .contact-list li a {
  color: var(--white);
}
.footer-widgets-wrapper.style-2 .footer-content .social-icon {
  margin-top: 40px;
  gap: 15px;
  position: relative;
  z-index: 9;
}
@media (max-width: 575px) {
  .footer-widgets-wrapper.style-2 .footer-content .social-icon {
    margin-top: 20px;
  }
}
.footer-widgets-wrapper.style-2 .footer-content .social-icon a {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 16px;
  display: block;
  color: var(--white);
  transition: all 0.4s ease-in-out;
  text-align: center;
  background-color: transparent;
  border: 1px solid #2c2e34;
  border-radius: 50%;
}
.footer-widgets-wrapper.style-2 .footer-content .social-icon a:hover {
  background-color: var(--theme);
  color: var(--white);
  border: 1px solid transparent;
}
.footer-widgets-wrapper.style-2 .list-items li a {
  color: var(--white);
  font-weight: 500;
}

.footer-bottom {
  border-top: 1px solid rgba(86, 101, 139, 0.1);
  padding: 30px 0;
  position: relative;
}
.footer-bottom .scroll-icon {
  width: 60px;
  height: 60px;
  line-height: 60px;
  border-radius: 14px;
  background-color: var(--theme);
  color: var(--white);
  display: inline-block;
  text-align: center;
  transition: all 0.4s ease-in-out;
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 99;
}
.footer-bottom .scroll-icon:hover {
  background-color: var(--header);
}
@media (max-width: 991px) {
  .footer-bottom .scroll-icon {
    top: -80px;
    left: 57%;
  }
}
.footer-bottom .footer-wrapper {
  position: relative;
  z-index: 9;
}
@media (max-width: 767px) {
  .footer-bottom .footer-wrapper {
    flex-wrap: wrap;
    justify-content: center !important;
    gap: 20px;
    margin-top: 15px;
  }
}
.footer-bottom .footer-wrapper p {
  color: var(--header);
  font-weight: 500;
}
.footer-bottom .footer-wrapper p span {
  color: var(--theme);
}
.footer-bottom .footer-wrapper .social-icon {
  gap: 13px;
}
.footer-bottom .footer-wrapper .social-icon a {
  width: 38px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  background-color: var(--white);
  box-shadow: 0px 1px 8px 0px rgba(1, 16, 61, 0.07);
  border-radius: 50%;
  display: inline-block;
  color: #9497a3;
}
.footer-bottom .footer-wrapper .social-icon a:hover {
  background-color: #01103d;
  color: var(--white);
}
.footer-bottom.style-2 {
  border: none;
  padding: 0;
  padding-bottom: 30px;
}
@media (max-width: 991px) {
  .footer-bottom.style-2 {
    text-align: center;
  }
}
.footer-bottom.style-2 .footer-wrapper {
  position: relative;
  z-index: 9;
}
.footer-bottom.style-2 .footer-wrapper p {
  color: var(--white);
}
.footer-bottom.style-2 .footer-wrapper p span {
  color: var(--white);
}
.footer-bottom.style-2 .footer-wrapper p a {
  color: var(--white);
}
.footer-bottom.style-2 .footer-wrapper .footer-menu {
  display: flex;
  align-items: center;
  gap: 20px;
}
.footer-bottom.style-2 .footer-wrapper .footer-menu li a {
  color: var(--white);
  background-image: linear-gradient(var(--white), var(--white));
  background-position: 0 95%;
  background-repeat: no-repeat;
  background-size: 0% 2px;
  display: inline-block;
  transition: all 0.4s ease-in-out;
}
.footer-bottom.style-2 .footer-wrapper .footer-menu li a:hover {
  background-size: 100% 1px;
}
.footer-bottom.style-2 .scroll-icon {
  background-color: var(--theme);
  color: var(--header);
}
.footer-bottom.style-2 .scroll-icon:hover {
  background-color: var(--white);
}

.footer-section {
  position: relative;
}
.footer-section .dot-shape {
  position: absolute;
  left: 7%;
  top: -5%;
}
.footer-section .footer-shape-1 {
  position: absolute;
  left: 0;
  bottom: 10%;
}
.footer-section .footer-shape-2 {
  position: absolute;
  top: 20%;
  right: 40px;
}
.footer-section .line-shape {
  position: absolute;
  left: 0;
  top: 20%;
}
.footer-section .star-shape {
  position: absolute;
  bottom: 110px;
  right: 70px;
}
.footer-section.style-2 {
  border-radius: 30px 30px 0px 0px;
}

.footer-newsletter-wrapper {
  padding: 100px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.12);
  margin-bottom: -3px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 1199px) {
  .footer-newsletter-wrapper {
    flex-wrap: wrap;
    gap: 30px;
  }
}
@media (max-width: 991px) {
  .footer-newsletter-wrapper {
    padding: 80px 0;
  }
}
.footer-newsletter-wrapper .newsletter-items {
  display: flex;
  align-items: center;
  gap: 40px;
}
@media (max-width: 575px) {
  .footer-newsletter-wrapper .newsletter-items {
    gap: 20px;
    flex-wrap: wrap;
  }
}
.footer-newsletter-wrapper .newsletter-items h2 {
  font-size: 37px;
  color: var(--white);
}
.footer-newsletter-wrapper .newsletter-input {
  max-width: 550px;
  position: relative;
  width: 100%;
}
.footer-newsletter-wrapper .newsletter-input input {
  width: 100%;
  border-radius: 100px;
  background-color: var(--white);
  line-height: 1;
  padding: 20px 30px;
  border: none;
  color: #70737d;
  position: relative;
}
.footer-newsletter-wrapper .newsletter-input input::placeholder {
  color: #70737d;
}
.footer-newsletter-wrapper .newsletter-input .newsletter-btn {
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 50%;
  background-color: var(--theme);
  color: var(--white);
  position: absolute;
  top: 5px;
  right: 5px;
  transition: all 0.4s ease-in-out;
}
.footer-newsletter-wrapper .newsletter-input .newsletter-btn i {
  transform: rotate(-40deg);
  transition: all 0.4s ease-in-out;
}
.footer-newsletter-wrapper .newsletter-input .newsletter-btn:hover {
  background-color: var(--header);
}
.footer-newsletter-wrapper .newsletter-input .newsletter-btn:hover i {
  transform: rotate(0);
}
.footer-newsletter-wrapper.style-2 {
  padding-top: 200px;
}

.header-section {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
}
.header-section .container-fluid {
  padding: 0 275px;
}
@media (max-width: 1899px) {
  .header-section .container-fluid {
    padding: 0 20px;
  }
}
.header-section .header-top-3 {
  background-color: var(--theme);
}

.header-section-2 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
}
.header-section-2 .container-fluid {
  padding: 0 250px;
}
@media (max-width: 1899px) {
  .header-section-2 .container-fluid {
    padding: 0 20px;
  }
}
@media (max-width: 1399px) {
  .header-section-2 {
    background-color: var(--white);
  }
}
.header-section-2::before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  content: "";
  background-color: var(--bg2);
  z-index: -1;
  height: 56%;
}
.header-section-2.style-two::before {
  display: none;
}

.header-top-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;
}
@media (max-width: 991px) {
  .header-top-wrapper {
    display: none;
  }
}
.header-top-wrapper p {
  font-weight: 700;
  color: var(--black);
}
.header-top-wrapper p a {
  color: var(--black);
}
.header-top-wrapper .header-top-right {
  display: flex;
  align-items: center;
  gap: 40px;
}
.header-top-wrapper .header-top-right .social-icon {
  gap: 20px;
}
.header-top-wrapper .header-top-right .social-icon a:hover {
  color: var(--theme);
}
.header-top-wrapper .header-top-right .nice-items {
  margin-right: 15px;
}
.header-top-wrapper .header-top-right .nice-items .nice-select {
  padding: 10px 7px 10px 20px;
  background: transparent;
  border: none;
  text-align: center;
  margin: 0 auto;
  position: relative;
  z-index: 999;
}
.header-top-wrapper .header-top-right .nice-items .nice-select span {
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  color: var(--header);
}
.header-top-wrapper .header-top-right .nice-items .nice-select::after {
  right: -10px;
  top: 24px;
}
.header-top-wrapper .header-top-right .nice-items .nice-select .list {
  background-color: var(--bg);
  border-radius: 0;
  right: initial;
  font-size: 14px;
  margin-top: 0;
}
.header-top-wrapper .header-top-right .nice-items .nice-select .list li {
  font-weight: 500;
}
.header-top-wrapper .header-top-right .nice-items .nice-select .option {
  border: none;
}
.header-top-wrapper.style-3 {
  margin-bottom: 0;
}
.header-top-wrapper.style-3 p {
  color: var(--white);
}
.header-top-wrapper.style-3 p a {
  color: var(--white);
}
.header-top-wrapper.style-3 .header-top-right .social-icon a {
  color: var(--white);
}
.header-top-wrapper.style-3 .header-top-right .social-icon a:hover {
  color: #ff003d;
}
.header-top-wrapper.style-3 .header-top-right .nice-items .nice-select span {
  color: var(--white);
}
.header-top-wrapper.style-3 .header-top-right .nice-items .nice-select::after {
  right: -10px;
  top: 24px;
  border-bottom: 2px solid var(--white);
  border-right: 2px solid var(--white);
}

.header-top-wrapper-2 {
  padding: 18px 30px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--white);
  position: relative;
}
@media (max-width: 1199px) {
  .header-top-wrapper-2 {
    display: none;
  }
}
.header-top-wrapper-2 .coming-soon {
  display: flex;
  align-items: center;
  gap: 24px;
}
.header-top-wrapper-2 .coming-soon ul {
  display: flex;
  align-items: center;
  gap: 20px;
}
.header-top-wrapper-2 .coming-soon ul li {
  font-size: 18px;
  font-weight: 700;
  color: var(--theme);
}
.header-top-wrapper-2 .coming-soon .theme-btn {
  border-radius: 9px;
  background-color: var(--theme);
  padding: 16.5px 22px;
  min-width: 140px;
}
.header-top-wrapper-2 .coming-soon .theme-btn::before {
  background-color: var(--header);
}
.header-top-wrapper-2 h6 {
  font-family: "Satoshi", sans-serif;
}
.header-top-wrapper-2 h6 i {
  color: var(--theme);
}
.header-top-wrapper-2 .header-top-right-2 {
  display: flex;
  align-items: center;
  gap: 35px;
}
.header-top-wrapper-2 .header-top-right-2 .social-icon {
  display: flex;
  align-items: center;
  gap: 20px;
}
.header-top-wrapper-2 .header-top-right-2 .social-icon a {
  color: var(--text);
}
.header-top-wrapper-2 .header-top-right-2 .social-icon a:hover {
  color: var(--theme);
}
.header-top-wrapper-2 .header-top-right-2 .flag-wrap {
  position: relative;
  width: 150px;
  background-color: #e8eaef;
  border-radius: 35px;
}
.header-top-wrapper-2 .header-top-right-2 .flag-wrap .nice-select {
  padding: 12px 7px 10px 45px;
  background: transparent;
  border: none;
  text-align: center;
  margin: 0 auto;
  position: relative;
  z-index: 999;
}
.header-top-wrapper-2 .header-top-right-2 .flag-wrap .nice-select span {
  font-size: 16px;
  font-weight: 700;
  text-transform: capitalize;
  color: var(--header);
}
.header-top-wrapper-2 .header-top-right-2 .flag-wrap .nice-select::after {
  height: 8px;
  width: 8px;
  right: 25px;
  top: 27px;
}
.header-top-wrapper-2 .header-top-right-2 .flag-wrap .nice-select .list {
  right: 0;
  background-color: var(--white);
}
.header-top-wrapper-2 .header-top-right-2 .flag-wrap .nice-select .option {
  border: none;
  padding: 4px 10px;
}
.header-top-wrapper-2 .header-top-right-2 .flag-wrap .flag {
  position: absolute;
  top: 9px;
  left: 15px;
  z-index: 1;
}
@media (max-width: 767px) {
  .header-top-wrapper-2 .header-top-right-2 .flag-wrap .flag {
    display: none;
  }
}
.header-top-wrapper-2 .header-top-right-2 .flag-wrap .flag img {
  width: 100%;
  height: 100%;
}

@media (max-width: 1199px) {
  .menu-thumb {
    display: none !important;
  }
}

.header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
}
.header-main .main-menu ul {
  margin-bottom: 0;
}
.header-main .main-menu ul li {
  position: relative;
  list-style: none;
  display: inline-block;
  margin-inline-end: 50px;
}
.header-main .main-menu ul li:last-child {
  margin-inline-end: 0;
}
.header-main .main-menu ul li a {
  display: inline-block;
  font-size: 16px;
  font-weight: 600;
  color: var(--header);
  padding: 20px 0;
  text-align: left;
  position: relative;
  text-transform: capitalize;
  font-weight: 700;
  transition: all 0.4s ease-in-out;
}
.header-main .main-menu ul li a i {
  margin-left: 4px;
}
.header-main .main-menu ul li a:hover {
  color: var(--theme) !important;
}
.header-main .main-menu ul li .submenu {
  position: absolute;
  top: 115%;
  inset-inline-start: 0;
  min-width: 240px;
  background: var(--white);
  padding: 20px 0;
  z-index: 99999;
  visibility: hidden;
  opacity: 0;
  transform-origin: top center;
  color: var(--header);
  box-shadow: rgba(149, 157, 165, 0.1) 0px 8px 24px;
  transform: translateY(-10px);
  transition: all 0.4s ease-in-out;
  border-top: 6px solid var(--theme);
}
.header-main .main-menu ul li .submenu li {
  display: block;
  width: 100%;
  margin: 0;
}
.header-main .main-menu ul li .submenu li a {
  position: relative;
  z-index: 11;
  font-size: 16px;
  font-weight: 700;
  color: var(--header);
  line-height: 38px;
  padding: 0px 0px 0px 32px;
  width: 100%;
}
.header-main .main-menu ul li .submenu li a::before {
  content: "";
  position: absolute;
  width: 0px;
  height: 2px;
  background: var(--theme);
  left: 14px;
  bottom: 18px;
  transition: all 0.4s ease-in-out;
}
.header-main .main-menu ul li .submenu li a:hover {
  color: var(--white) !important;
}
.header-main .main-menu ul li .submenu li:last-child a {
  border: none;
}
.header-main .main-menu ul li .submenu li .submenu {
  inset-inline-start: 100%;
  top: 0;
  visibility: hidden;
  opacity: 0;
}
.header-main .main-menu ul li .submenu li:hover > a {
  color: var(--theme) !important;
  margin-left: 10px;
}
.header-main .main-menu ul li .submenu li:hover > a::before {
  width: 10px;
}
.header-main .main-menu ul li .submenu li:hover > a::after {
  color: var(--theme);
}
.header-main .main-menu ul li .submenu li:hover > .submenu {
  -webkit-transform: translateY(1);
  -moz-transform: translateY(1);
  -ms-transform: translateY(1);
  -o-transform: translateY(1);
  transform: translateY(1);
  visibility: visible;
  opacity: 1;
}
.header-main .main-menu ul li .submenu li.has-dropdown > a::after {
  position: absolute;
  top: 50%;
  inset-inline-end: 25px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  color: var(--theme);
}
.header-main .main-menu ul li .has-homemenu {
  width: 800px;
  padding: 30px 30px 10px 30px;
  opacity: 0;
  left: -250px;
  visibility: hidden;
  padding: 30px 30px 10px 30px;
}
.header-main .main-menu ul li .has-homemenu .homemenu-items {
  display: flex;
  align-items: center;
  gap: 30px;
  justify-content: space-between;
}
@media (max-width: 991px) {
  .header-main .main-menu ul li .has-homemenu .homemenu-items {
    flex-wrap: wrap;
  }
}
.header-main .main-menu ul li .has-homemenu .homemenu-items .homemenu {
  position: relative;
}
.header-main
  .main-menu
  ul
  li
  .has-homemenu
  .homemenu-items
  .homemenu
  .homemenu-thumb {
  position: relative;
}
.header-main
  .main-menu
  ul
  li
  .has-homemenu
  .homemenu-items
  .homemenu
  .homemenu-thumb
  .demo-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70%;
  gap: 10px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease-in-out;
  margin-top: 20px;
}
.header-main
  .main-menu
  ul
  li
  .has-homemenu
  .homemenu-items
  .homemenu
  .homemenu-thumb
  .demo-button
  .theme-btn {
  padding: 14px 20px;
  color: var(--white) !important;
  width: initial;
  font-size: 16px;
  text-align: center;
  border-radius: 0px !important;
  background-color: var(--theme);
  line-height: initial;
}
.header-main
  .main-menu
  ul
  li
  .has-homemenu
  .homemenu-items
  .homemenu
  .homemenu-thumb
  .demo-button
  .theme-btn:hover {
  color: var(--white) !important;
  background-color: var(--header) !important;
}
.header-main
  .main-menu
  ul
  li
  .has-homemenu
  .homemenu-items
  .homemenu
  .homemenu-thumb
  .demo-button
  .theme-btn:hover::before {
  background-color: var(--header) !important;
}
.header-main
  .main-menu
  ul
  li
  .has-homemenu
  .homemenu-items
  .homemenu
  .homemenu-thumb::before {
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    from(rgba(20, 19, 19, 0)),
    to(#5e5ef6)
  );
  background: linear-gradient(to bottom, rgba(99, 92, 92, 0) 0%, #252527 100%);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
  opacity: 0;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  content: "";
}
.header-main
  .main-menu
  ul
  li
  .has-homemenu
  .homemenu-items
  .homemenu
  .homemenu-thumb:hover::before {
  visibility: visible;
  opacity: 1;
}
.header-main
  .main-menu
  ul
  li
  .has-homemenu
  .homemenu-items
  .homemenu
  .homemenu-thumb:hover
  .demo-button {
  opacity: 1;
  visibility: visible;
  margin-top: 0;
}
.header-main
  .main-menu
  ul
  li
  .has-homemenu
  .homemenu-items
  .homemenu
  .homemenu-thumb:hover
  .homemenu-btn {
  opacity: 1;
  visibility: visible;
  bottom: 50%;
  transform: translateY(50%);
}
.header-main
  .main-menu
  ul
  li
  .has-homemenu
  .homemenu-items
  .homemenu
  .homemenu-thumb
  img {
  width: 100%;
}
.header-main
  .main-menu
  ul
  li
  .has-homemenu
  .homemenu-items
  .homemenu
  .homemenu-title {
  text-align: center;
  margin: 15px auto;
  display: inline-block;
  font-size: 16px;
}
.header-main .main-menu ul li:hover > a {
  color: var(--theme);
}
.header-main .main-menu ul li:hover > a::after {
  color: var(--theme);
}
.header-main .main-menu ul li:hover > .submenu {
  visibility: visible;
  opacity: 1;
  transform: translateY(0px);
}
.header-main .header-right {
  gap: 40px;
}
@media (max-width: 1399px) {
  .header-main .header-right {
    gap: 20px;
  }
}
.header-main .sidebar__toggle {
  cursor: pointer;
  font-size: 20px;
}

@media (max-width: 1199px) {
  .header-1 {
    background-color: var(--white);
  }
}
.header-1 .mega-menu-wrapper {
  background-color: var(--white);
  padding: 0 30px;
  border-radius: 0px 0px 9px 9px;
  position: relative;
}
@media (max-width: 1199px) {
  .header-1 .mega-menu-wrapper {
    background-color: var(--white);
    border-radius: 0;
    padding: 0;
  }
}
.header-1 .mega-menu-wrapper::before {
  position: absolute;
  right: 122px;
  content: "";
  top: 0;
  bottom: 0;
  width: 1px;
  height: 85px;
  background-color: #ececec;
}
@media (max-width: 1399px) {
  .header-1 .mega-menu-wrapper::before {
    display: none;
  }
}
.header-1 .header-right {
  gap: 80px;
}
@media (max-width: 1399px) {
  .header-1 .header-right {
    gap: 30px;
  }
}
@media (max-width: 575px) {
  .header-1 .header-right {
    gap: 15px;
  }
}
.header-1 .header-right .cart-title {
  font-weight: 700;
  font-family: "Satoshi", sans-serif;
  display: flex;
  align-items: center;
  gap: 6px;
  line-height: 1;
}
.header-1 .header-right .cart-title span {
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  border-radius: 50%;
  background-color: var(--header);
  color: var(--white);
  text-align: center;
  font-size: 12px;
}
.header-1 .header-right .search-icon {
  color: var(--header);
  font-size: 20px;
}
.header-1 .header-right .sidebar__toggle {
  color: var(--header);
}
@media (max-width: 1199px) {
  .header-1.style-2 {
    background-color: var(--white);
  }
}
.header-1.style-2 .mega-menu-wrapper {
  border-radius: 0;
}
@media (max-width: 1399px) {
  .header-1.style-2 .mega-menu-wrapper {
    background-color: var(--white);
    border-radius: 0;
    padding: 0;
  }
}
.header-1.style-2 .mega-menu-wrapper::before {
  display: none;
}
.header-1.style-2 .mega-menu-wrapper .header-main .main-menu ul li {
  margin-inline-end: 40px;
}
.header-1.style-2 .mega-menu-wrapper .header-main .main-menu ul li:last-child {
  margin-inline-end: 0;
}
.header-1.style-2 .mega-menu-wrapper .header-right {
  gap: 35px;
}
@media (max-width: 767px) {
  .header-1.style-2 .mega-menu-wrapper .header-right {
    gap: 20px;
  }
}
@media (max-width: 1199px) {
  .header-1.style-2 .mega-menu-wrapper .header-right .header-button {
    display: none;
  }
}
.header-1.style-2 .mega-menu-wrapper .header-right .header-button .theme-btn {
  border-radius: 9px;
  padding: 19.5px 40px;
}
@media (max-width: 1399px) {
  .header-1.style-2 .mega-menu-wrapper .header-right .content {
    display: none;
  }
}
.header-1.style-2 .mega-menu-wrapper .header-right .content p {
  font-size: 14px;
  text-transform: uppercase;
}
.header-1.style-2 .mega-menu-wrapper .header-right .content h5 {
  font-family: "Satoshi", sans-serif;
}
.header-1.style-2 .mega-menu-wrapper .header-right .menu-cart {
  position: relative;
}
.header-1.style-2 .mega-menu-wrapper .header-right .menu-cart .cart-icon {
  position: relative;
  width: 50px;
  height: 50px;
  line-height: 50px;
  border-radius: 50%;
  text-align: center;
  background-color: #edf4fd;
  display: inline-block;
}
.header-1.style-2
  .mega-menu-wrapper
  .header-right
  .menu-cart
  .cart-icon::before {
  position: absolute;
  top: -3px;
  right: 0px;
  content: "0";
  width: 16px;
  line-height: 16px;
  height: 16px;
  border-radius: 16px;
  background-color: var(--header);
  color: var(--white);
  font-size: 12px;
  text-align: center;
  font-weight: 500;
}
.header-1.style-2 .mega-menu-wrapper .header-right .menu-cart .cart-icon i {
  color: #d12525;
}
.header-1.style-2 .mega-menu-wrapper .header-right .menu-cart:hover .cart-box {
  transform: rotateX(0deg);
  visibility: visible;
}
.header-1.style-3 .mega-menu-wrapper::before {
  position: absolute;
  right: 105px;
  content: "";
  top: 0;
  bottom: 0;
  width: 1px;
  height: 85px;
  background-color: #ececec;
}
@media (max-width: 1399px) {
  .header-1.style-3 .mega-menu-wrapper::before {
    display: none;
  }
}
.header-1.style-3 .header-right {
  gap: 100px;
}
@media (max-width: 1399px) {
  .header-1.style-3 .header-right {
    gap: 35px;
  }
}
@media (max-width: 575px) {
  .header-1.style-3 .header-right {
    gap: 25px;
  }
}
.header-1.style-3 .header-right .cart-item {
  display: flex;
  align-items: center;
  gap: 18px;
}
@media (max-width: 1199px) {
  .header-1.style-3 .header-right .cart-item {
    display: none;
  }
}
.header-1.style-3 .header-right .cart-item .cart-icon {
  position: relative;
}
.header-1.style-3 .header-right .cart-item .cart-icon::before {
  position: absolute;
  top: -7px;
  right: -8px;
  content: "0";
  width: 16px;
  line-height: 16px;
  height: 16px;
  border-radius: 16px;
  background-color: #ff0b45;
  color: var(--white);
  font-size: 12px;
  text-align: center;
  font-weight: 500;
}
.header-1.style-3 .header-right .cart-item .cart-icon i {
  color: var(--header);
  font-size: 20px;
}
.header-1.style-3 .header-right .cart-item .content h6 {
  font-family: "Satoshi", sans-serif;
}
.header-1.style-3 .header-right .cart-item .content p {
  color: #73839c;
  font-size: 13px;
  font-weight: 700;
}

.sticky {
  position: fixed !important;
  top: 0 !important;
  left: 0;
  width: 100%;
  z-index: 100;
  transition: all 0.9s;
  background-color: var(--white);
  -webkit-animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
  animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
  box-shadow: var(--box-shadow);
}
.sticky.header-1 .mega-menu-wrapper {
  padding: 0;
  border-radius: 0;
}
.sticky.header-1 .mega-menu-wrapper::before {
  right: 100px;
}

.offcanvas__info {
  background: var(--bg) none repeat scroll 0 0;
  border-left: 2px solid var(--theme);
  position: fixed;
  right: 0;
  top: 0;
  width: 400px;
  height: 100%;
  -webkit-transform: translateX(calc(100% + 80px));
  -moz-transform: translateX(calc(100% + 80px));
  -ms-transform: translateX(calc(100% + 80px));
  -o-transform: translateX(calc(100% + 80px));
  transform: translateX(calc(100% + 80px));
  -webkit-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  -moz-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  z-index: 99999;
  overflow-y: scroll;
  overscroll-behavior-y: contain;
  scrollbar-width: none;
}
.offcanvas__info::-webkit-scrollbar {
  display: none;
}

.offcanvas__info.info-open {
  opacity: 1;
  -webkit-transform: translateX(0);
  -moz-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  transform: translateX(0);
}

.offcanvas__logo a img {
  width: 150px;
}

.offcanvas__wrapper {
  position: relative;
  height: 100%;
  padding: 30px 30px;
}
.offcanvas__wrapper .offcanvas__content .text {
  color: var(--text);
}
.offcanvas__wrapper .offcanvas__content .offcanvas__close {
  width: 45px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  border-radius: 50%;
  background-color: var(--theme);
  position: relative;
  z-index: 9;
  cursor: pointer;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__close i {
  color: var(--white);
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact {
  margin-top: 20px;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul {
  margin-top: 20px;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul li {
  font-size: 16px;
  font-weight: 600;
  text-transform: capitalize;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact ul li a {
  color: var(--text);
}
.offcanvas__wrapper
  .offcanvas__content
  .offcanvas__contact
  ul
  li:not(:last-child) {
  margin-bottom: 15px;
}
.offcanvas__wrapper
  .offcanvas__content
  .offcanvas__contact
  ul
  li
  .offcanvas__contact-icon {
  margin-right: 20px;
}
.offcanvas__wrapper
  .offcanvas__content
  .offcanvas__contact
  ul
  li
  .offcanvas__contact-icon
  i {
  color: var(--theme);
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact span {
  text-transform: initial;
}
.offcanvas__wrapper
  .offcanvas__content
  .offcanvas__contact
  .header-button
  .theme-btn {
  width: 100%;
  padding: 16px 40px;
  text-transform: capitalize !important;
  color: var(--white);
  font-weight: 700;
  border-radius: 4px;
  background-color: var(--theme);
}
.offcanvas__wrapper
  .offcanvas__content
  .offcanvas__contact
  .header-button
  .theme-btn
  i {
  margin-left: 10px;
}
.offcanvas__wrapper
  .offcanvas__content
  .offcanvas__contact
  .header-button
  .theme-btn::before {
  background-color: var(--header);
}
.offcanvas__wrapper
  .offcanvas__content
  .offcanvas__contact
  .header-button
  .theme-btn:hover {
  background-color: var(--white);
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact .social-icon {
  margin-top: 30px;
  gap: 10px;
}
.offcanvas__wrapper .offcanvas__content .offcanvas__contact .social-icon a {
  width: 45px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  font-size: 16px;
  display: block;
  background: transparent;
  color: var(--text);
  border-radius: 50%;
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
  text-align: center;
  border: 1px solid var(--border);
}
.offcanvas__wrapper
  .offcanvas__content
  .offcanvas__contact
  .social-icon
  a:hover {
  background-color: var(--theme);
  color: var(--white);
}

.offcanvas__overlay {
  position: fixed;
  height: 100%;
  width: 100%;
  background: #151515;
  z-index: 900;
  top: 0;
  opacity: 0;
  visibility: hidden;
  right: 0;
}

.offcanvas__overlay.overlay-open {
  opacity: 0.8;
  visibility: visible;
}

@media (max-width: 450px) {
  .offcanvas__info {
    width: 300px;
  }
}
@media (max-width: 575px) {
  .offcanvas__wrapper {
    padding: 20px;
  }
}
.breadcrumb-section {
  padding-top: 88px;
}
@media (max-width: 1600px) {
  .breadcrumb-section {
    padding-top: 0;
  }
}
.breadcrumb-section .container-fluid {
  padding: 0 68px;
}
@media (max-width: 1899px) {
  .breadcrumb-section .container-fluid {
    padding: 0 20px;
  }
}

.breadcrumb-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 25px;
}
@media (max-width: 1899px) {
  .breadcrumb-wrapper {
    border-radius: 0;
  }
}
.breadcrumb-wrapper .flower-shape {
  position: absolute;
  top: 90px;
  left: 40px;
  animation: translateY2 2s forwards infinite alternate;
}
@media (max-width: 767px) {
  .breadcrumb-wrapper .flower-shape {
    display: none;
  }
}
.breadcrumb-wrapper .star-shape {
  position: absolute;
  right: 80px;
  bottom: 70px;
  animation: translateX2 2s forwards infinite alternate;
}
@media (max-width: 767px) {
  .breadcrumb-wrapper .star-shape {
    display: none;
  }
}
.breadcrumb-wrapper .page-heading {
  position: relative;
  padding: 200px 0 130px;
  z-index: 9;
}
@media (max-width: 1600px) {
  .breadcrumb-wrapper .page-heading {
    padding-top: 250px;
  }
}
@media (max-width: 1199px) {
  .breadcrumb-wrapper .page-heading {
    padding-top: 190px;
  }
}
@media (max-width: 767px) {
  .breadcrumb-wrapper .page-heading {
    padding: 170px 0 100px;
  }
}
@media (max-width: 575px) {
  .breadcrumb-wrapper .page-heading {
    padding: 150px 0 80px;
  }
}
.breadcrumb-wrapper .page-heading h6 {
  font-size: 18px;
  font-weight: 500;
  font-family: "Space Grotesk", sans-serif;
  background-color: var(--theme);
  color: var(--white);
  padding: 14px 30px;
  border-radius: 50px;
  display: inline-block;
  margin-bottom: 25px;
  line-height: 1;
}
@media (max-width: 575px) {
  .breadcrumb-wrapper .page-heading h6 {
    font-size: 16px;
    padding: 14px 20px;
  }
}
.breadcrumb-wrapper .page-heading h1 {
  color: var(--header);
  font-size: 80px;
  position: relative;
  z-index: 9;
  font-weight: 800;
}
@media (max-width: 991px) {
  .breadcrumb-wrapper .page-heading h1 {
    font-size: 68px;
  }
}
@media (max-width: 575px) {
  .breadcrumb-wrapper .page-heading h1 {
    font-size: 45px;
  }
}

.error-content {
  margin-top: -58px;
}
@media (max-width: 1199px) {
  .error-content {
    margin-top: -42px;
  }
}
@media (max-width: 991px) {
  .error-content {
    margin-top: -30px;
  }
}
@media (max-width: 575px) {
  .error-content {
    margin-top: -18px;
  }
}
.error-content h2 {
  font-weight: 700;
  font-size: 400px;
  line-height: 1;
}
@media (max-width: 1199px) {
  .error-content h2 {
    font-size: 300px;
  }
}
@media (max-width: 991px) {
  .error-content h2 {
    font-size: 200px;
  }
}
@media (max-width: 575px) {
  .error-content h2 {
    font-size: 110px;
  }
}
.error-content h3 {
  font-weight: 700;
  font-size: 55px;
  text-transform: uppercase;
}
@media (max-width: 991px) {
  .error-content h3 {
    font-size: 48px;
  }
}
@media (max-width: 575px) {
  .error-content h3 {
    font-size: 36px;
  }
}
@media (max-width: 575px) {
  .error-content h3 {
    font-size: 36px;
  }
}
.error-content .theme-btn {
  border-radius: 9px;
  background-color: var(--theme);
}
.error-content .theme-btn::before {
  background-color: var(--header);
}

::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

/* Track */
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px var(--theme);
  border-radius: 5px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: var(--theme);
  border-radius: 10px;
}

.fix {
  overflow: hidden;
}

.ralt {
  position: relative;
}

.ml-50 {
  margin-left: 50px;
}
@media (max-width: 1199px) {
  .ml-50 {
    margin-left: 0;
  }
}

.ripple {
  position: relative;
}
.ripple::before,
.ripple::after {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 180px;
  height: 180px;
  -webkit-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
  border-radius: 50%;
  box-shadow: 0 0 0 0 rgba(56, 75, 255, 0.5);
  -webkit-animation: rippleOne 3s infinite;
  animation: rippleOne 3s infinite;
}
@media (max-width: 767px) {
  .ripple::before,
  .ripple::after {
    width: 160px;
    height: 160px;
  }
}
@media (max-width: 575px) {
  .ripple::before,
  .ripple::after {
    width: 130px;
    height: 130px;
  }
}
.ripple::before {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
  content: "";
  position: absolute;
  right: 0;
  bottom: 0;
}
.ripple::after {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
  content: "";
  position: absolute;
  right: 0;
  bottom: 0;
}

.swiper-dot {
  text-align: center;
}
.swiper-dot .swiper-pagination-bullet {
  width: 65px;
  height: 5px;
  transition: 0.6s;
  background-color: var(--white);
  opacity: 1;
  border-radius: 32.5px;
  position: relative;
}
.swiper-dot .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: var(--theme);
  transition: 0.6s;
  position: relative;
  width: 65px;
}

.swiper-dot-2 .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  transition: 0.6s;
  background-color: #d6d1d1;
  opacity: 1;
  border-radius: 10px;
}
.swiper-dot-2 .swiper-pagination-bullet:not(:last-child) {
  margin-right: 10px;
}
.swiper-dot-2 .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: #ff003d;
  transition: 0.6s;
  position: relative;
}
.swiper-dot-2
  .swiper-pagination-bullet.swiper-pagination-bullet-active::before {
  position: absolute;
  width: 22px;
  height: 22px;
  line-height: 22px;
  top: -6px;
  left: -6px;
  border-radius: 50%;
  background-color: transparent;
  border: 2px solid #ff003d;
  content: "";
}

.array-button {
  display: flex;
  align-items: center;
  gap: 15px;
}
.array-button .array-prev {
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center;
  background-color: transparent;
  color: var(--white);
  border-radius: 50%;
  transition: all 0.4s ease-in-out;
  border: 1px solid var(--white);
}
.array-button .array-prev:hover {
  background-color: var(--white);
  color: var(--header);
}
.array-button .array-next {
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center;
  background-color: var(--theme2);
  color: var(--white);
  border-radius: 50%;
  border: 1px solid transparent;
  transition: all 0.4s ease-in-out;
}
.array-button .array-next:hover {
  background-color: var(--white);
  color: var(--header);
}

.mt-10 {
  margin-top: 10px;
}

@media (max-width: 767px) {
  br {
    display: none;
  }
}

.mt-60 {
  margin-top: 55px;
}

.mb-40 {
  margin-bottom: 40px;
}

/* background */
.bg-cover {
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  background-position: center;
}

.bg-cover-2 {
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  background-position: center;
  width: 100%;
  height: 100%;
}

.nice-select {
  background-color: transparent;
  border: transparent;
  float: initial;
  overflow: initial;
  height: initial;
  padding: 0;
  display: inline-flex;
  align-items: center;
  line-height: 150%;
  width: 100%;
  border: none;
}
.nice-select:focus,
.nice-select:hover {
  border-color: transparent;
}
.nice-select::after {
  height: 8px;
  width: 8px;
  right: -25px;
  top: 15px;
  border-color: var(--header);
  border-bottom: 2px solid var(--header);
  border-right: 2px solid var(--header);
}
.nice-select .list {
  width: initial;
  background-color: var(--theme);
  box-shadow: none;
  overflow: initial;
  box-shadow: rgba(0, 0, 0, 0.15) 0px 3px 3px 0px;
  width: 100%;
  top: 100%;
  padding: 0;
  max-height: 50vh;
  overflow-x: auto;
  right: -50px;
}
.nice-select .list::-webkit-scrollbar {
  width: 2px;
  opacity: 1;
  display: block;
}
.nice-select .list::-webkit-scrollbar-button,
.nice-select .list::-webkit-scrollbar-thumb {
  background: var(--header);
}
.nice-select .option {
  background-color: transparent;
  font-size: 16px;
  line-height: 150%;
  padding: 4px 5px;
  min-height: initial;
  font-weight: 500;
}
.nice-select .option:hover,
.nice-select .option:focus,
.nice-select .option.selected.focus {
  background-color: transparent;
}
.nice-select .current {
  font-weight: 500;
  color: var(--header);
}

.scroll-up {
  cursor: pointer;
  display: block;
  border-radius: 50px;
  box-shadow: inset 0 0 0 2px var(--border);
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px);
  position: fixed;
  right: 25px;
  bottom: 35px;
  height: 50px;
  width: 50px;
  transition: all 0.4s ease-in-out;
}

.scroll-up::after {
  position: absolute;
  font-family: "Font Awesome 6 free";
  content: "\f062";
  text-align: center;
  line-height: 50px;
  font-weight: 700;
  font-size: 18px;
  color: var(--theme);
  left: 0;
  top: 0;
  height: 50px;
  width: 50px;
  cursor: pointer;
  display: block;
  z-index: 1;
  transition: all 0.4s ease-in-out;
}

.scroll-up svg path {
  fill: none;
}

.scroll-up svg.scroll-circle path {
  stroke: var(--theme);
  stroke-width: 4px;
  box-sizing: border-box;
  transition: all 0.4s ease-in-out;
}

.scroll-up.active-scroll {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.page-nav-wrap ul li {
  display: inline-block;
}
.page-nav-wrap ul li .page-numbers {
  display: inline-block;
  width: 50px;
  height: 50px;
  line-height: 50px;
  background: var(--white);
  font-weight: 600;
  transition: all 0.3s ease-in-out;
  margin: 0 2px;
  border: 1px solid #ededed;
  color: #3f4b58;
  border-radius: 50%;
}
.page-nav-wrap ul li .page-numbers.current {
  background-color: var(--theme);
  color: var(--white);
}
@media (max-width: 767px) {
  .page-nav-wrap ul li .page-numbers {
    margin-top: 10px;
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 14px;
  }
}
.page-nav-wrap ul li .page-numbers i {
  margin-top: 2px;
}
.page-nav-wrap ul li .page-numbers.icon {
  border: none;
  background-color: transparent;
  color: var(--theme);
}
.page-nav-wrap ul li .page-numbers.icon:hover {
  border: none;
  background-color: transparent;
  color: var(--theme);
}
.page-nav-wrap ul li .page-numbers:hover {
  background-color: var(--theme2);
  color: var(--white);
  border: 1px solid transparent;
}

.bor-bottom {
  border-bottom: 1px solid var(--border-2);
}

.pt-100 {
  padding-top: 100px;
}
@media (max-width: 991px) {
  .pt-100 {
    padding-top: 80px;
  }
}

.pb-100 {
  padding-bottom: 100px;
}
@media (max-width: 991px) {
  .pb-100 {
    padding-bottom: 80px;
  }
}

.margin-top-10 {
  margin-top: -10px;
}

.margin-top-5 {
  margin-top: -5px;
}

.margin-top-60 {
  margin-top: -65px;
}
@media (max-width: 1399px) {
  .margin-top-60 {
    margin-top: -25px;
  }
}
@media (max-width: 1199px) {
  .margin-top-60 {
    margin-top: -27px;
  }
}
@media (max-width: 991px) {
  .margin-top-60 {
    margin-top: -75px;
  }
}
@media (max-width: 767px) {
  .margin-top-60 {
    margin-top: -65px;
  }
}

.margin-top-8 {
  margin-top: -10px;
}

.margin-top-20 {
  margin-top: -20px;
}

.margin-bottom-25 {
  margin-bottom: -25px;
}

.custom-container {
  max-width: 1328px;
  margin: 0 auto;
}

.custom-container-2 {
  max-width: 1365px;
  margin: 0 auto;
}

.border-radius-22 {
  border-radius: 22px !important;
}

.mb-80 {
  margin-bottom: 85px;
}

.reveal {
  position: relative;
  display: -ms-inline-flexbox;
  visibility: hidden;
  overflow: hidden;
}

.reveal img {
  display: inline-block;
  -o-object-fit: cover;
  object-fit: cover;
  transform-origin: left;
}

.image-anime {
  position: relative;
  overflow: hidden;
}

.hero-1 {
  padding: 250px 0 100px;
  border-radius: 0px 0px 90px 90px;
  position: relative;
}
@media (max-width: 1199px) {
  .hero-1 {
    border-radius: 0;
  }
}
@media (max-width: 991px) {
  .hero-1 {
    padding: 200px 0 100px;
  }
}
@media (max-width: 767px) {
  .hero-1 {
    padding: 150px 0 100px;
  }
}
.hero-1 .circle-shape {
  position: absolute;
  left: 42px;
  top: 15%;
  animation: cir36 10s linear infinite;
}
.hero-1 .circle-shape-2 {
  position: absolute;
  bottom: 105px;
  right: 80px;
  animation: cir36 10s linear infinite;
}
.hero-1 .vector-shape {
  position: absolute;
  left: 75px;
  bottom: 22%;
}
.hero-1 .arrow-shape {
  position: absolute;
  top: 45%;
  right: 72px;
}
.hero-1 .scroll-down {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 99;
  cursor: pointer;
}
.hero-1 .hero-content {
  position: relative;
  z-index: 9;
}
.hero-1 .hero-content h6 {
  font-size: 16px;
  font-weight: 700;
  font-family: "Space Grotesk", sans-serif;
  background-color: var(--theme);
  color: var(--header);
  color: var(--white);
  padding: 14px 30px;
  border-radius: 50px;
  display: inline-block;
  margin-bottom: 20px;
  line-height: 1;
}
@media (max-width: 1399px) {
  .hero-1 .hero-content h1 {
    font-size: 62px;
  }
}
@media (max-width: 1199px) {
  .hero-1 .hero-content h1 {
    font-size: 54px;
  }
}
@media (max-width: 991px) {
  .hero-1 .hero-content h1 {
    font-size: 68px;
  }
}
@media (max-width: 767px) {
  .hero-1 .hero-content h1 {
    font-size: 56px;
  }
}
@media (max-width: 575px) {
  .hero-1 .hero-content h1 {
    font-size: 42px;
  }
}
.hero-1 .hero-content h1 span {
  position: relative;
  z-index: 1;
  display: inline-block;
}
.hero-1 .hero-content h1 span img {
  position: absolute;
  bottom: 10px;
  left: 0;
  z-index: -1;
}
@media (max-width: 767px) {
  .hero-1 .hero-content h1 span img {
    display: none;
  }
}
.hero-1 .hero-content p {
  font-size: 20px;
  font-weight: 700;
  line-height: 160%;
  margin-top: 30px;
}
@media (max-width: 575px) {
  .hero-1 .hero-content p {
    font-size: 16px;
  }
}
.hero-1 .hero-content .list {
  margin-top: 30px;
  margin-bottom: 50px;
}
.hero-1 .hero-content .list li {
  font-size: 16px;
  color: #696969;
  font-weight: 700;
}
@media (max-width: 575px) {
  .hero-1 .hero-content .list li {
    font-size: 16px;
  }
}
.hero-1 .hero-content .list li i {
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 50%;
  background-color: var(--white);
  color: #6f32f0;
  margin-right: 5px;
}
@media (max-width: 575px) {
  .hero-1 .hero-content .list li i {
    width: 25px;
    height: 25px;
    line-height: 25px;
  }
}
.hero-1 .hero-content .list li:not(:last-child) {
  margin-bottom: 20px;
}
.hero-1 .hero-thumb {
  margin-bottom: -100px;
  position: relative;
  z-index: 9;
  width: 800px;
  margin-left: -165px;
}
@media (max-width: 1399px) {
  .hero-1 .hero-thumb {
    margin-bottom: -135px;
    width: 640px;
  }
}
@media (max-width: 1199px) {
  .hero-1 .hero-thumb {
    margin-left: 0;
    width: initial;
  }
}
@media (max-width: 991px) {
  .hero-1 .hero-thumb {
    margin-bottom: -100px;
    width: 730px;
  }
}
@media (max-width: 767px) {
  .hero-1 .hero-thumb {
    margin-left: 0;
    width: initial;
  }
}
.hero-1 .hero-thumb::before {
  position: absolute;
  top: 0;
  left: 50%;
  width: 600px;
  height: 600px;
  border-radius: 50%;
  opacity: 0.8;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.75) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  content: "";
  z-index: -1;
  transform: translateX(-50%);
}
@media (max-width: 1399px) {
  .hero-1 .hero-thumb::before {
    display: none;
  }
}
@media (max-width: 991px) {
  .hero-1 .hero-thumb::before {
    display: initial;
  }
}
@media (max-width: 575px) {
  .hero-1 .hero-thumb::before {
    display: none;
  }
}
.hero-1 .hero-thumb img {
  width: 100%;
  height: 100%;
}
.hero-1 .hero-thumb .information-shape {
  position: absolute;
  top: 40px;
  right: -18%;
}
.hero-1 .hero-thumb .information-shape img {
  max-width: initial;
}
@media (max-width: 1600px) {
  .hero-1 .hero-thumb .information-shape {
    display: none;
  }
}

.hero-2 {
  position: relative;
  z-index: 9;
  margin: 0 68px;
  border-radius: 30px;
  overflow: hidden;
  margin-top: 88px;
}
@media (max-width: 1600px) {
  .hero-2 {
    margin: 0;
    border-radius: 0;
  }
}
@media (max-width: 1399px) {
  .hero-2 {
    padding: 180px 0;
  }
  .hero-2::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    content: "";
    background-color: var(--black);
    opacity: 0.4;
  }
}
@media (max-width: 991px) {
  .hero-2 {
    padding: 150px 0;
  }
}
@media (max-width: 767px) {
  .hero-2 {
    padding: 140px 0;
  }
}
@media (max-width: 575px) {
  .hero-2 {
    padding: 120px 0;
  }
}
.hero-2 .flower-shape {
  position: absolute;
  top: 90px;
  left: 70px;
}
.hero-2 .rong-shape {
  position: absolute;
  left: 70px;
  bottom: 70px;
}
@media (max-width: 1399px) {
  .hero-2 .rong-shape {
    display: none;
  }
}
.hero-2 .stickers-shape {
  position: absolute;
  right: 60px;
  bottom: 70px;
}
.hero-2 .hero-content {
  padding: 175px 130px;
  width: 900px;
  position: relative;
  z-index: 9;
  margin-top: 90px;
  background-repeat: no-repeat;
  margin-bottom: 30px;
  margin-left: 40px;
}
@media (max-width: 1600px) {
  .hero-2 .hero-content {
    margin-top: 0;
    margin-left: 0;
  }
}
@media (max-width: 1399px) {
  .hero-2 .hero-content {
    background-image: none !important;
    width: initial;
    padding: 0;
  }
}
@media (max-width: 767px) {
  .hero-2 .hero-content {
    text-align: center;
  }
}
.hero-2 .hero-content h5 {
  color: var(--white);
  font-size: 18px;
  font-weight: 500;
  text-transform: capitalize;
  background: rgba(255, 255, 255, 0.15);
  padding: 12px 20px;
  line-height: 1;
  display: inline-block;
  margin-bottom: 25px;
  border-radius: 22px;
}
.hero-2 .hero-content h1 {
  font-size: 62px;
  color: var(--white);
  letter-spacing: -1.44px;
  line-height: 102%;
  margin-bottom: 30px;
}
@media (max-width: 991px) {
  .hero-2 .hero-content h1 {
    font-size: 62px;
  }
}
@media (max-width: 767px) {
  .hero-2 .hero-content h1 {
    font-size: 55px;
  }
}
@media (max-width: 575px) {
  .hero-2 .hero-content h1 {
    font-size: 45px;
  }
}
.hero-2 .hero-content p {
  font-size: 18px;
  color: var(--white);
}
.hero-2 .hero-content .hero-button {
  margin-top: 30px;
}

.hero-3 {
  padding: 250px 0 120px;
  position: relative;
}
@media (max-width: 991px) {
  .hero-3 {
    padding: 190px 0 120px;
  }
}
@media (max-width: 575px) {
  .hero-3 {
    padding: 160px 0 120px;
  }
}
.hero-3 .line-shape {
  position: absolute;
  bottom: 50px;
  left: 30px;
  animation: rounded 5s linear infinite;
}
@media (max-width: 1199px) {
  .hero-3 .line-shape {
    display: none;
  }
}
.hero-3 .hero-content {
  opacity: 0;
  z-index: 3;
  position: relative;
  transform: translateY(-150px);
}
.hero-3 .hero-content h6 {
  font-size: 18px;
  font-weight: 500;
  font-family: "Space Grotesk", sans-serif;
  border-radius: 18.5px;
  background: #ff003d;
  color: var(--white);
  padding: 12px 30px;
  border-radius: 50px;
  display: inline-block;
  margin-bottom: 20px;
  line-height: 1;
  text-transform: capitalize;
}
.hero-3 .hero-content h1 {
  text-transform: capitalize;
}
@media (max-width: 991px) {
  .hero-3 .hero-content h1 {
    font-size: 62px;
  }
}
@media (max-width: 767px) {
  .hero-3 .hero-content h1 {
    font-size: 55px;
  }
}
@media (max-width: 575px) {
  .hero-3 .hero-content h1 {
    font-size: 36px;
  }
}
.hero-3 .hero-content h1 span {
  position: relative;
  z-index: 1;
  display: inline-block;
}
.hero-3 .hero-content h1 span img {
  position: absolute;
  bottom: 8px;
  left: 0;
  z-index: -1;
}
@media (max-width: 767px) {
  .hero-3 .hero-content h1 span img {
    display: none;
  }
}
.hero-3 .hero-content p {
  font-size: 20px;
  color: #6e707a;
  margin-top: 30px;
}
.hero-3 .hero-button {
  margin-top: 40px;
  transform: translateY(150px);
  opacity: 0;
}
.hero-3 .hero-button .theme-btn {
  padding: 25px 65px;
}
.hero-3 .hero-image-items {
  position: relative;
  z-index: 9;
  margin-left: -100px;
}
.hero-3 .hero-image-items .hero-image {
  position: relative;
  z-index: 9;
  transform: translateY(-150px);
  opacity: 0;
}
@media (max-width: 1600px) {
  .hero-3 .hero-image-items .hero-image {
    margin-left: -100px;
  }
}
@media (max-width: 1399px) {
  .hero-3 .hero-image-items .hero-image {
    margin-left: 0;
  }
}
.hero-3 .hero-image-items .hero-image::before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  content: "";
  width: 570px;
  height: 570px;
  border-radius: 50%;
  box-shadow: 0px 18px 41px 0px #bad8ec;
  background-color: rgba(255, 255, 255, 0.6);
  z-index: -1;
}
@media (max-width: 1399px) {
  .hero-3 .hero-image-items .hero-image::before {
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}
@media (max-width: 767px) {
  .hero-3 .hero-image-items .hero-image::before {
    display: none;
  }
}
.hero-3 .hero-image-items .hero-image img {
  margin-left: -25px;
  margin-top: 15px;
}
@media (max-width: 1399px) {
  .hero-3 .hero-image-items .hero-image img {
    width: 100%;
    margin: 0;
  }
}
.hero-3 .hero-image-items .color-box {
  position: absolute;
  top: -81px;
  left: -16%;
  z-index: 99;
  transform: translateX(-150px);
  opacity: 0;
}
@media (max-width: 1600px) {
  .hero-3 .hero-image-items .color-box {
    left: 0;
  }
}
@media (max-width: 1199px) {
  .hero-3 .hero-image-items .color-box {
    display: none;
  }
}
@media (max-width: 991px) {
  .hero-3 .hero-image-items .color-box {
    display: block;
  }
}
@media (max-width: 767px) {
  .hero-3 .hero-image-items .color-box {
    display: none;
  }
}
.hero-3 .hero-image-items .mockup-shape {
  position: absolute;
  bottom: -72px;
  left: -24%;
  z-index: 99;
  transform: translateY(150px);
  opacity: 0;
}
@media (max-width: 1600px) {
  .hero-3 .hero-image-items .mockup-shape {
    left: 0;
  }
}
@media (max-width: 1199px) {
  .hero-3 .hero-image-items .mockup-shape {
    display: none;
  }
}
@media (max-width: 991px) {
  .hero-3 .hero-image-items .mockup-shape {
    display: block;
  }
}
@media (max-width: 767px) {
  .hero-3 .hero-image-items .mockup-shape {
    display: none;
  }
}
.hero-3 .hero-image-items .stickers-shape {
  position: absolute;
  top: -30px;
  right: -20px;
  z-index: 99;
  transform: translateX(150px);
  opacity: 0;
}
@media (max-width: 1600px) {
  .hero-3 .hero-image-items .stickers-shape {
    right: 0;
  }
}
@media (max-width: 1199px) {
  .hero-3 .hero-image-items .stickers-shape {
    display: none;
  }
}
@media (max-width: 991px) {
  .hero-3 .hero-image-items .stickers-shape {
    display: block;
  }
}
@media (max-width: 767px) {
  .hero-3 .hero-image-items .stickers-shape {
    display: none;
  }
}
.hero-3 .hero-image-items .cap-shape {
  position: absolute;
  bottom: 0;
  right: -20%;
  z-index: 99;
  transform: translateY(150px);
  opacity: 0;
}
@media (max-width: 1600px) {
  .hero-3 .hero-image-items .cap-shape {
    right: 0;
  }
}
@media (max-width: 1199px) {
  .hero-3 .hero-image-items .cap-shape {
    display: none;
  }
}
@media (max-width: 991px) {
  .hero-3 .hero-image-items .cap-shape {
    display: block;
  }
}
@media (max-width: 767px) {
  .hero-3 .hero-image-items .cap-shape {
    display: none;
  }
}
.hero-3 .swiper {
  overflow: initial;
}
.hero-3 .swiper-slide.swiper-slide-active .hero-content {
  opacity: 1;
  transform: translateY(0px);
  transition: all 2500ms ease;
}
.hero-3 .swiper-slide.swiper-slide-active .hero-button {
  opacity: 1;
  transform: translateY(0px);
  transition: all 2500ms ease;
}
.hero-3 .swiper-slide.swiper-slide-active .hero-image-items .hero-image {
  opacity: 1;
  transform: translateY(0px);
  transition: all 2500ms ease;
}
.hero-3 .swiper-slide.swiper-slide-active .hero-image-items .color-box {
  opacity: 1;
  transform: translateX(0px);
  transition: all 2500ms ease;
}
.hero-3 .swiper-slide.swiper-slide-active .hero-image-items .mockup-shape {
  opacity: 1;
  transform: translateY(0px);
  transition: all 2500ms ease;
}
.hero-3 .swiper-slide.swiper-slide-active .hero-image-items .stickers-shape {
  opacity: 1;
  transform: translateX(0px);
  transition: all 2500ms ease;
}
.hero-3 .swiper-slide.swiper-slide-active .hero-image-items .cap-shape {
  opacity: 1;
  transform: translateY(0px);
  transition: all 2500ms ease;
}

.mean-container a.meanmenu-reveal {
  display: none;
}

.mean-container .mean-nav {
  background: none;
  margin-top: 0;
}

.mean-container .mean-bar {
  padding: 0;
  min-height: auto;
  background: none;
}

.mean-container .mean-nav > ul {
  padding: 0;
  margin: 0;
  width: 100%;
  list-style-type: none;
  display: block !important;
}
.mean-container .mean-nav > ul .homemenu-items {
  display: flex;
  align-items: center;
  gap: 30px;
  justify-content: space-between;
}
@media (max-width: 1199px) {
  .mean-container .mean-nav > ul .homemenu-items {
    flex-wrap: wrap;
  }
}
.mean-container .mean-nav > ul .homemenu-items .homemenu {
  position: relative;
}
@media (max-width: 1199px) {
  .mean-container .mean-nav > ul .homemenu-items .homemenu {
    max-width: 300px;
    text-align: center;
    margin: 0 auto;
    border: 1px solid var(--border);
    padding: 10px;
  }
}
.mean-container .mean-nav > ul .homemenu-items .homemenu .homemenu-thumb {
  position: relative;
}
.mean-container
  .mean-nav
  > ul
  .homemenu-items
  .homemenu
  .homemenu-thumb
  .demo-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  gap: 10px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease-in-out;
  margin-top: 20px;
}
.mean-container
  .mean-nav
  > ul
  .homemenu-items
  .homemenu
  .homemenu-thumb
  .demo-button
  .theme-btn {
  padding: 12px 20px;
  color: var(--white) !important;
  width: initial;
  font-size: 16px;
  text-align: center;
  border-radius: 0;
}
.mean-container
  .mean-nav
  > ul
  .homemenu-items
  .homemenu
  .homemenu-thumb
  .demo-button
  .theme-btn:hover {
  color: var(--white) !important;
}
.mean-container
  .mean-nav
  > ul
  .homemenu-items
  .homemenu
  .homemenu-thumb::before {
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    from(rgba(20, 19, 19, 0)),
    to(#5e5ef6)
  );
  background: linear-gradient(to bottom, rgba(99, 92, 92, 0) 0%, #252527 100%);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
  opacity: 0;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  content: "";
}
.mean-container
  .mean-nav
  > ul
  .homemenu-items
  .homemenu
  .homemenu-thumb:hover::before {
  visibility: visible;
  opacity: 1;
}
.mean-container
  .mean-nav
  > ul
  .homemenu-items
  .homemenu
  .homemenu-thumb:hover
  .demo-button {
  opacity: 1;
  visibility: visible;
  margin-top: 0;
}
.mean-container
  .mean-nav
  > ul
  .homemenu-items
  .homemenu
  .homemenu-thumb:hover
  .homemenu-btn {
  opacity: 1;
  visibility: visible;
  bottom: 50%;
  transform: translateY(50%);
}
.mean-container .mean-nav > ul .homemenu-items .homemenu .homemenu-thumb img {
  width: 100%;
}
.mean-container .mean-nav > ul .homemenu-items .homemenu .homemenu-title {
  text-align: center;
  margin: 15px auto;
  display: inline-block;
  font-size: 16px;
}

.mean-container a.meanmenu-reveal {
  display: none !important;
}

.mean-container .mean-nav ul li a {
  width: 100%;
  padding: 10px 0;
  color: var(--header);
  font-size: 16px;
  line-height: 1.5;
  font-weight: 500;
  text-transform: capitalize;
  border-bottom: 1px solid var(--border) !important;
  border: none;
}
.mean-container .mean-nav ul li a:hover {
  color: var(--theme);
}

.mean-container .mean-nav ul li a:last-child {
  border-bottom: 0;
}

.mean-container .mean-nav ul li a:hover {
  color: var(--theme);
}

.mean-container .mean-nav ul li a.mean-expand {
  margin-top: 5px;
  padding: 0 !important;
}

.mean-container .mean-nav ul li > a > i {
  display: none;
}

.mean-container .mean-nav ul li > a.mean-expand i {
  display: inline-block;
  font-size: 18px;
}

.mean-container .mean-nav > ul > li:first-child > a {
  border-top: 0;
}

.mean-container .mean-nav ul li a.mean-expand.mean-clicked i {
  transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transition: all 0.4s ease-in-out;
}

.mean-container .mean-nav ul li .mega-menu li a {
  height: 200px;
  width: 100%;
  padding: 0;
  border-top: 0;
  margin-bottom: 20px;
}

.blog-section {
  margin: 0 60px;
  border-radius: 53px;
}
@media (max-width: 1600px) {
  .blog-section {
    margin: 0;
    border-radius: 0;
  }
}
.blog-section .section-title h6 {
  background: #fec9ff;
  color: var(--header);
}

.blog-box-items {
  margin-top: 30px;
}
.blog-box-items .blog-image {
  position: relative;
  overflow: hidden;
}
.blog-box-items .blog-image img {
  width: 100%;
  height: 100%;
  position: relative;
  display: block;
  object-fit: cover;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  border-radius: 15px;
}
.blog-box-items .blog-image img:first-child {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  -webkit-transform: translateX(50%) scaleX(2);
  transform: translateX(50%) scaleX(2);
  opacity: 0;
  -webkit-filter: blur(10px);
  filter: blur(10px);
  border-radius: 15px;
  transition: all 0.4s ease-in-out;
}
.blog-box-items .blog-content {
  margin-top: 25px;
  margin-left: 20px;
}
.blog-box-items .blog-content span {
  color: #ff1b1b;
  font-size: 16px;
  font-weight: 600;
  display: inline-block;
  margin-bottom: 15px;
}
.blog-box-items .blog-content h3 {
  padding-bottom: 20px;
}
.blog-box-items .blog-content h3 a:hover {
  color: var(--theme);
}
.blog-box-items .blog-content .blog-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid rgba(0, 23, 38, 0.1);
  padding-top: 20px;
}
.blog-box-items .blog-content .blog-meta li i {
  margin-right: 6px;
}
.blog-box-items:hover .blog-image img:first-child {
  -webkit-transform: translateX(0) scaleX(1);
  transform: translateX(0) scaleX(1);
  opacity: 1;
  -webkit-filter: blur(0);
  filter: blur(0);
}
.blog-box-items:hover .blog-image img:nth-child(2) {
  -webkit-transform: translateX(-50%) scaleX(2);
  transform: translateX(-50%) scaleX(2);
  opacity: 0;
  -webkit-filter: blur(10px);
  filter: blur(10px);
}

.blog-wrapper {
  margin-bottom: 130px;
}

.news-standard-wrapper .news-standard-items .news-thumb {
  position: relative;
}
.news-standard-wrapper .news-standard-items .news-thumb img {
  width: 100%;
  height: 100%;
  border-radius: 20px;
}
.news-standard-wrapper .news-standard-items .news-thumb .post-cat {
  position: absolute;
  top: 15px;
  left: 15px;
  font-size: 20px;
  background-color: var(--theme);
  color: var(--white);
  font-weight: 700;
  padding: 10px 25px;
  border-radius: 12px;
}
.news-standard-wrapper .news-standard-items .news-content {
  margin-top: 30px;
}
.news-standard-wrapper .news-standard-items .news-content .post-meta {
  margin-bottom: 20px;
}
.news-standard-wrapper .news-standard-items .news-content .post-meta span {
  color: var(--text);
  font-weight: 500;
  font-size: 18px;
  line-height: 1;
  display: inline-block;
  margin-right: 25px;
}
@media (max-width: 767px) {
  .news-standard-wrapper .news-standard-items .news-content .post-meta span {
    font-size: 15px;
  }
}
.news-standard-wrapper .news-standard-items .news-content .post-meta span i {
  margin-right: 10px;
  color: var(--theme);
  font-weight: 700;
}
.news-standard-wrapper .news-standard-items .news-content h3 {
  font-size: 32px;
  margin-bottom: 20px;
}
.news-standard-wrapper .news-standard-items .news-content h3 a:hover {
  color: var(--theme);
}
.news-standard-wrapper .news-standard-items .news-content .link-btns {
  font-weight: 600;
  text-transform: uppercase;
  font-size: 16px;
  display: inline-block;
  margin-top: 30px;
}
.news-standard-wrapper .news-standard-items .news-content .link-btns i {
  margin-left: 10px;
  font-weight: 700;
}
.news-standard-wrapper .news-standard-items .news-content .link-btns:hover {
  color: var(--theme);
}
.news-standard-wrapper .news-standard-items:not(:last-child) {
  margin-bottom: 40px;
}

.main-sidebar .news-profile-items {
  text-align: center;
}
.main-sidebar .news-profile-items .content {
  margin-top: 25px;
}
.main-sidebar .news-profile-items .content h6 {
  text-transform: uppercase;
  margin-bottom: 5px;
}
.main-sidebar .news-profile-items .content span {
  font-size: 14px;
  display: inline-block;
  margin-bottom: 15px;
}
.main-sidebar .news-profile-items .content .social-icon {
  justify-content: center;
  gap: 13px;
  margin-top: 30px;
}
.main-sidebar .news-profile-items .content .social-icon a {
  width: 44px;
  height: 37px;
  line-height: 37px;
  text-align: center;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  color: var(--text);
  display: inline-block;
}
.main-sidebar .news-profile-items .content .social-icon a:hover {
  background-color: var(--theme);
  color: var(--white);
}
.main-sidebar .single-sidebar-widget {
  background-color: #f4f6f8;
  padding: 40px 35px;
  border-radius: 15px;
}
.main-sidebar .single-sidebar-widget .wid-title {
  margin-bottom: 20px;
  padding-left: 10px;
  position: relative;
}
.main-sidebar .single-sidebar-widget .wid-title::before {
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  content: "";
  width: 2px;
  height: 21px;
  position: absolute;
  background-color: var(--theme);
}
.main-sidebar .single-sidebar-widget .wid-title h4 {
  font-size: 22px;
  font-weight: 700;
}
.main-sidebar .single-sidebar-widget:not(:last-child) {
  margin-bottom: 30px;
}
.main-sidebar .single-sidebar-widget .search_widget form {
  width: 100%;
  position: relative;
}
.main-sidebar .single-sidebar-widget .search_widget form input {
  background-color: var(--white);
  font-size: 15px;
  width: 100%;
  border: none;
  text-transform: capitalize;
  color: #9d9e9f;
  border-radius: 7px;
  padding: 12px 22px;
}
.main-sidebar .single-sidebar-widget .search_widget form button {
  position: absolute;
  right: 24px;
  top: 12px;
  font-size: 18px;
  color: var(--header);
  text-align: center;
  transition: all 0.3s ease-in-out;
}
.main-sidebar .single-sidebar-widget .news-widget-categories ul li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 20px;
  background-color: var(--white);
  font-weight: 500;
  transition: all 0.4s ease-in-out;
  border: 1px solid #f8f1ee;
  border-radius: 9px;
  font-size: 16px;
  line-height: 1;
}
.main-sidebar .single-sidebar-widget .news-widget-categories ul li a {
  color: #445658;
}
.main-sidebar .single-sidebar-widget .news-widget-categories ul li span {
  transition: all 0.4s ease-in-out;
  color: var(--header);
}
.main-sidebar
  .single-sidebar-widget
  .news-widget-categories
  ul
  li:not(:last-child) {
  margin-bottom: 12px;
}
.main-sidebar .single-sidebar-widget .news-widget-categories ul li:hover {
  background-color: var(--theme);
}
.main-sidebar .single-sidebar-widget .news-widget-categories ul li:hover a {
  color: var(--white);
}
.main-sidebar .single-sidebar-widget .news-widget-categories ul li:hover span {
  color: var(--white);
}
.main-sidebar .single-sidebar-widget .news-widget-categories ul li.active {
  background-color: var(--theme);
}
.main-sidebar .single-sidebar-widget .news-widget-categories ul li.active a {
  color: var(--white);
}
.main-sidebar .single-sidebar-widget .news-widget-categories ul li.active span {
  color: var(--white);
}
.main-sidebar .single-sidebar-widget .recent-post-area .recent-items {
  display: flex;
  align-items: center;
  gap: 20px;
}
.main-sidebar
  .single-sidebar-widget
  .recent-post-area
  .recent-items:not(:last-child) {
  margin-bottom: 25px;
}
.main-sidebar
  .single-sidebar-widget
  .recent-post-area
  .recent-items
  .recent-thumb
  img {
  border-radius: 9px;
}
.main-sidebar
  .single-sidebar-widget
  .recent-post-area
  .recent-items
  .recent-content
  ul {
  margin-bottom: 8px;
}
.main-sidebar
  .single-sidebar-widget
  .recent-post-area
  .recent-items
  .recent-content
  ul
  li {
  color: var(--text);
  font-size: 15px;
}
.main-sidebar
  .single-sidebar-widget
  .recent-post-area
  .recent-items
  .recent-content
  ul
  li
  i {
  color: var(--theme);
  margin-right: 5px;
}
.main-sidebar
  .single-sidebar-widget
  .recent-post-area
  .recent-items
  .recent-content
  h6 {
  font-weight: 600;
}
.main-sidebar
  .single-sidebar-widget
  .recent-post-area
  .recent-items
  .recent-content
  h6
  a:hover {
  color: var(--theme);
}
.main-sidebar .single-sidebar-widget .tagcloud a {
  display: inline-block;
  padding: 11px 20px;
  line-height: 1;
  font-size: 16px;
  font-weight: 500;
  background: var(--white);
  margin-right: 5px;
  text-transform: capitalize;
  margin-bottom: 10px;
  border-radius: 9px;
  color: var(--text);
  transition: all 0.4s ease-in-out;
}
.main-sidebar .single-sidebar-widget .tagcloud a:last-child {
  margin-right: 0;
}
.main-sidebar .single-sidebar-widget .tagcloud a:hover {
  background-color: var(--theme);
  color: var(--white);
}

.news-details-wrapper .single-news-post .details-image img {
  width: 100%;
  height: 100%;
  border-radius: 11px;
}
.news-details-wrapper .single-news-post .details-content {
  margin-top: 30px;
}
.news-details-wrapper .single-news-post .details-content .post-meta {
  margin-bottom: 20px;
}
.news-details-wrapper .single-news-post .details-content .post-meta span {
  color: var(--text);
  font-weight: 500;
  font-size: 18px;
  line-height: 1;
  display: inline-block;
  margin-right: 25px;
}
@media (max-width: 767px) {
  .news-details-wrapper .single-news-post .details-content .post-meta span {
    font-size: 15px;
  }
}
.news-details-wrapper .single-news-post .details-content .post-meta span i {
  margin-right: 10px;
  color: var(--theme);
  font-weight: 700;
}
.news-details-wrapper .single-news-post .details-content h3 {
  font-size: 32px;
  margin-bottom: 20px;
}
.news-details-wrapper .single-news-post .details-content h3 a:hover {
  color: var(--theme);
}
.news-details-wrapper .single-news-post .details-content p {
  font-size: 18px;
  color: #838383;
  font-weight: 400;
}
.news-details-wrapper .single-news-post .details-content .hilight-text {
  padding: 40px;
  background-color: #e8ecef;
}
.news-details-wrapper .single-news-post .details-content .hilight-text p {
  font-weight: 700;
  text-transform: capitalize;
  font-family: "Space Grotesk", sans-serif;
  color: var(--header);
  line-height: 180%;
  font-size: 24px;
}
.news-details-wrapper .single-news-post .details-content .hilight-text svg {
  float: right;
  margin-top: -30px;
}
.news-details-wrapper .single-news-post .details-content .hilight-text h6 {
  font-weight: 500;
  padding-left: 30px;
  margin-top: 20px;
  position: relative;
}
.news-details-wrapper
  .single-news-post
  .details-content
  .hilight-text
  h6::before {
  position: absolute;
  top: 8px;
  left: 0;
  content: "";
  width: 16px;
  height: 2px;
  background-color: var(--header);
}
.news-details-wrapper .single-news-post .details-content h4 {
  font-size: 28px;
  font-weight: 700;
}
.news-details-wrapper .single-news-post .details-content .details-list li {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 400;
}
.news-details-wrapper
  .single-news-post
  .details-content
  .details-list
  li:not(:last-child) {
  margin-bottom: 20px;
}
.news-details-wrapper .single-news-post .details-content .details-list li i {
  font-size: 24px;
  color: var(--theme);
}
.news-details-wrapper .single-news-post .details-content .post-thumb img {
  width: 100%;
  height: 100%;
  border-radius: 10px;
}
.news-details-wrapper .tag-share-wrap {
  border-bottom: 1px solid #f6f6f6;
  padding: 30px 0 40px;
}
.news-details-wrapper .tag-share-wrap .tagcloud span {
  display: inline-block;
  font-weight: 700;
  color: var(--header);
  margin-right: 15px;
  font-size: 20px;
}
.news-details-wrapper .tag-share-wrap .tagcloud a {
  display: inline-block;
  padding: 12px 26px;
  line-height: 1;
  background: var(--bg);
  margin-right: 8px;
  text-transform: capitalize;
  font-weight: 500;
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
  border-radius: 0;
}
@media (max-width: 575px) {
  .news-details-wrapper .tag-share-wrap .tagcloud a {
    margin-bottom: 5px;
  }
}
.news-details-wrapper .tag-share-wrap .tagcloud a:hover {
  background-color: var(--theme);
  color: var(--white);
}
.news-details-wrapper .tag-share-wrap .social-share span {
  font-size: 20px;
  color: var(--header);
  font-weight: 700;
}
.news-details-wrapper .tag-share-wrap .social-share a {
  font-size: 16px;
  color: var(--text);
}
.news-details-wrapper .tag-share-wrap .social-share a:not(:last-child) {
  margin-right: 10px;
}
.news-details-wrapper .tag-share-wrap .social-share a:hover {
  color: var(--theme);
}
.news-details-wrapper .comments-area {
  margin-top: 40px;
}
.news-details-wrapper .comments-area .comments-heading {
  margin-bottom: 30px;
}
@media (max-width: 575px) {
  .news-details-wrapper .comments-area .comments-heading {
    margin-bottom: 20px;
  }
}
.news-details-wrapper .comments-area .comments-heading h3 {
  font-size: 28px;
  font-weight: 700;
}
@media (max-width: 575px) {
  .news-details-wrapper .comments-area .comments-heading h3 {
    font-size: 22px;
  }
}
@media (max-width: 575px) {
  .news-details-wrapper .comments-area .blog-single-comment {
    flex-wrap: wrap;
    gap: 20px;
  }
}
.news-details-wrapper .comments-area .blog-single-comment p {
  font-weight: 400;
}
.news-details-wrapper
  .comments-area
  .blog-single-comment
  .content
  .head
  .con
  h5 {
  font-weight: 600;
  font-size: 20px;
}
.news-details-wrapper
  .comments-area
  .blog-single-comment
  .content
  .head
  .con
  span {
  font-size: 12px;
  text-transform: uppercase;
  color: var(--text);
  font-weight: 400;
}
.news-details-wrapper
  .comments-area
  .blog-single-comment
  .content
  .head
  .reply {
  font-size: 20px;
  color: var(--header);
}
@media (min-width: 1200px) {
  .news-details-wrapper .comments-area .blog-single-comment.style-2 {
    margin-left: 80px;
  }
}
.news-details-wrapper .comment-form-wrap {
  background: #f0f2f9;
  padding: 50px;
  margin-top: 30px;
}
.news-details-wrapper .comment-form-wrap h3 {
  font-size: 28px;
  margin-bottom: 30px;
}
@media (max-width: 575px) {
  .news-details-wrapper .comment-form-wrap h3 {
    font-size: 28px;
    margin-bottom: 20px;
  }
}
.news-details-wrapper .comment-form-wrap .form-clt input,
.news-details-wrapper .comment-form-wrap .form-clt textarea {
  width: 100%;
  outline: none;
  border: none;
  background-color: var(--white);
  padding: 16px 20px;
  font-weight: 400;
  color: var(--text);
}
.news-details-wrapper .comment-form-wrap .form-clt input::placeholder,
.news-details-wrapper .comment-form-wrap .form-clt textarea::placeholder {
  color: var(--text);
}
.news-details-wrapper .comment-form-wrap .form-clt textarea {
  padding-bottom: 230px;
}
.news-details-wrapper .comment-form-wrap .from-customradio .form-check-label {
  color: #838383;
  font-size: 16px;
  text-transform: capitalize;
  margin-bottom: 20px;
  font-weight: 400;
}
.news-details-wrapper .comment-form-wrap .theme-btn {
  background-color: var(--theme);
  border-radius: 7px;
}
.news-details-wrapper .comment-form-wrap .theme-btn::before {
  background-color: var(--header);
}

.preloader {
  align-items: center;
  cursor: default;
  display: flex;
  height: 100%;
  justify-content: center;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 9999999;
}
.preloader .animation-preloader {
  z-index: 1000;
}
.preloader .animation-preloader .spinner {
  animation: spinner 1s infinite linear;
  border-radius: 50%;
  border: 3px solid rgba(0, 0, 0, 0.2);
  border-top-color: var(--theme);
  height: 9em;
  margin: 0 auto 3.5em auto;
  width: 9em;
}
@media (max-width: 767px) {
  .preloader .animation-preloader .spinner {
    width: 7.5em;
    height: 7.5em;
    margin: 0 auto 1.5em auto;
  }
}
.preloader .animation-preloader .txt-loading {
  font: bold 5em "Space Grotesk", sans-serif, "Satoshi", sans-serif;
  text-align: center;
  user-select: none;
}
@media (max-width: 767px) {
  .preloader .animation-preloader .txt-loading {
    font-size: 2.5em;
  }
}
.preloader .animation-preloader .txt-loading .letters-loading {
  color: var(--theme);
  position: relative;
}
.preloader
  .animation-preloader
  .txt-loading
  .letters-loading:nth-child(2):before {
  animation-delay: 0.2s;
}
.preloader
  .animation-preloader
  .txt-loading
  .letters-loading:nth-child(3):before {
  animation-delay: 0.4s;
}
.preloader
  .animation-preloader
  .txt-loading
  .letters-loading:nth-child(4):before {
  animation-delay: 0.6s;
}
.preloader
  .animation-preloader
  .txt-loading
  .letters-loading:nth-child(5):before {
  animation-delay: 0.8s;
}
.preloader
  .animation-preloader
  .txt-loading
  .letters-loading:nth-child(6):before {
  animation-delay: 1s;
}
.preloader
  .animation-preloader
  .txt-loading
  .letters-loading:nth-child(7):before {
  animation-delay: 1.2s;
}
.preloader
  .animation-preloader
  .txt-loading
  .letters-loading:nth-child(8):before {
  animation-delay: 1.4s;
}
.preloader .animation-preloader .txt-loading .letters-loading::before {
  animation: letters-loading 4s infinite;
  color: var(--header);
  content: attr(data-text-preloader);
  left: 0;
  opacity: 0;
  font-family: "Space Grotesk", sans-serif;
  position: absolute;
  top: -3px;
  transform: rotateY(-90deg);
}
.preloader p {
  font-size: 15px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 8px;
  color: var(--theme);
}
.preloader .loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-size: 0;
  z-index: 1;
  pointer-events: none;
}
.preloader .loader .row {
  height: 100%;
}
.preloader .loader .loader-section {
  padding: 0px;
}
.preloader .loader .loader-section .bg {
  background-color: var(--bg);
  height: 100%;
  left: 0;
  width: 100%;
  transition: all 800ms cubic-bezier(0.77, 0, 0.175, 1);
}
.preloader.loaded .animation-preloader {
  opacity: 0;
  transition: 0.3s ease-out;
}
.preloader.loaded .loader-section .bg {
  width: 0;
  transition: 0.7s 0.3s allcubic-bezier(0.1, 0.1, 0.1, 1);
}

.search-wrap {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999999;
  background-color: rgba(255, 255, 255, 0.9);
}
.search-wrap .search-inner {
  position: relative;
  width: 100%;
  height: 100%;
}
.search-wrap .search-cell {
  position: absolute;
  top: 50%;
  width: 100%;
  transform: translateY(-50%);
}
.search-wrap .search-field-holder {
  width: 50%;
  margin: auto;
  position: relative;
  animation: slideInUp 0.3s;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .search-wrap .search-field-holder {
    width: 70%;
  }
}
@media (max-width: 575px) {
  .search-wrap .search-field-holder {
    width: 80%;
  }
}
.search-wrap .main-search-input {
  width: 100%;
  height: 70px;
  border: 0;
  padding: 0 50px;
  text-transform: capitalize;
  background: transparent;
  font-size: 25px;
  color: var(--theme);
  border-bottom: 2px solid var(--theme);
  text-align: center;
  letter-spacing: 2px;
}

@media (max-width: 575px) {
  .search-wrap .main-search-input {
    height: 50px;
    padding: 0 0;
    line-height: 50px;
    font-size: 18px;
  }
}
.search-wrap input.form-control,
.search-wrap input.form-control:focus {
  background-color: var(--theme);
}

input.main-search-input::placeholder {
  color: var(--theme);
  opacity: 1;
  font-size: 25px;
}

@media (max-width: 575px) {
  input.main-search-input::placeholder {
    font-size: 18px;
  }
}
.search-close {
  position: absolute;
  top: 50px;
  right: 50px;
  font-size: 30px;
  color: var(--theme);
  cursor: pointer;
}

.mouse-cursor {
  position: fixed;
  left: 0;
  top: 0;
  pointer-events: none;
  border-radius: 50%;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  visibility: hidden;
}

.cursor-inner {
  width: 6px;
  height: 6px;
  z-index: 10000001;
  background-color: var(--theme);
  -webkit-transition: width 0.3s ease-in-out, height 0.3s ease-in-out,
    margin 0.3s ease-in-out, opacity 0.3s ease-in-out;
  -o-transition: width 0.3s ease-in-out, height 0.3s ease-in-out,
    margin 0.3s ease-in-out, opacity 0.3s ease-in-out;
  transition: width 0.3s ease-in-out, height 0.3s ease-in-out,
    margin 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

.cursor-inner.cursor-hover {
  margin-left: -35px;
  margin-top: -35px;
  width: 70px;
  height: 70px;
  background-color: var(--theme);
  opacity: 0.3;
}

.cursor-outer {
  margin-left: -12px;
  margin-top: -12px;
  width: 30px;
  height: 30px;
  border: 1px solid var(--theme);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  z-index: 10000000;
  opacity: 0.5;
  -webkit-transition: all 0.08s ease-out;
  -o-transition: all 0.08s ease-out;
  transition: all 0.08s ease-out;
}

.cursor-outer.cursor-hover {
  opacity: 0;
}

.pricing-box-items {
  margin-top: 30px;
  background: var(--white);
  border-radius: 20px;
  box-shadow: 0px 1px 14px 0px rgba(0, 0, 0, 0.13);
  padding: 50px 60px;
  transition: all 0.4s ease-in-out;
  position: relative;
}
@media (max-width: 991px) {
  .pricing-box-items {
    padding: 45px 35px;
  }
}
@media (max-width: 767px) {
  .pricing-box-items {
    padding: 40px 30px;
  }
}
.pricing-box-items .pricing-tag {
  position: absolute;
  top: 0;
  right: 0;
}
.pricing-box-items .pricing-header {
  border-bottom: 1px solid #e8eaf1;
  padding-bottom: 20px;
}
.pricing-box-items .pricing-header .icon {
  font-size: 50px;
  color: #6865ff;
  margin-bottom: 20px;
}
.pricing-box-items .pricing-header span {
  font-weight: 400;
}
.pricing-box-items .pricing-list {
  margin-top: 25px;
}
.pricing-box-items .pricing-list li {
  font-weight: 400;
}
.pricing-box-items .pricing-list li:not(:last-child) {
  margin-bottom: 20px;
}
.pricing-box-items .pricing-list li i {
  color: #6865ff;
  margin-right: 10px;
}
.pricing-box-items .pricing-btn {
  margin-top: 50px;
}
.pricing-box-items .pricing-btn .theme-btn {
  background: rgba(104, 101, 255, 0.08);
  color: #6865ff;
  width: 100%;
}
.pricing-box-items .pricing-btn .theme-btn:hover {
  color: var(--white);
}
.pricing-box-items .pricing-btn .theme-btn:hover .theme-effect {
  color: var(--white);
}
.pricing-box-items .pricing-btn .theme-btn:hover .theme-effect .effect-1 {
  top: -100%;
}
.pricing-box-items .pricing-btn .theme-btn .theme-effect {
  text-transform: capitalize;
  position: relative;
  transition: all 0.4s ease-in-out;
  color: var(--theme);
  z-index: 9;
  position: relative;
  overflow: hidden;
  display: block;
  height: 21px;
  line-height: normal;
  transition: all 0.4s ease-in-out;
}
.pricing-box-items .pricing-btn .theme-btn .theme-effect .effect-1 {
  display: block;
  height: 100%;
  position: relative;
  top: 0%;
  transition: all 0.4s ease-in-out;
}
.pricing-box-items.active {
  background: #600ee4;
  box-shadow: 0px 1px 14px 0px rgba(18, 20, 32, 0.07);
}
.pricing-box-items.active .pricing-header h3,
.pricing-box-items.active .pricing-header span {
  color: var(--white);
}
.pricing-box-items.active .pricing-header .icon {
  color: var(--white);
  filter: grayscale(100%) brightness(300%);
}
.pricing-box-items.active .pricing-list li {
  color: var(--white);
}
.pricing-box-items.active .pricing-list li i {
  color: var(--white);
}
.pricing-box-items.active .pricing-btn .theme-btn {
  background: var(--header);
  color: var(--white);
}
.pricing-box-items.active .pricing-btn .theme-btn .theme-effect {
  color: var(--white);
}
.pricing-box-items:hover {
  transform: translateY(-10px);
}

.pricing-tab-header {
  margin-top: 40px;
  margin-bottom: 30px;
  position: relative;
}
@media (max-width: 767px) {
  .pricing-tab-header {
    margin-bottom: 0;
  }
}
.pricing-tab-header .nav {
  display: flex;
  align-items: center;
  gap: 0;
  background-color: var(--white);
  border-radius: 50px;
  box-shadow: 0px 1px 1px 0px rgba(18, 20, 32, 0.14);
  padding: 6px;
  justify-content: center;
  max-width: 245px;
  margin: 0 auto;
}
@media (max-width: 991px) {
  .pricing-tab-header .nav {
    max-width: 325px;
  }
}
@media (max-width: 575px) {
  .pricing-tab-header .nav {
    gap: 15px;
    max-width: 225px;
  }
}
.pricing-tab-header .nav .nav-link {
  text-align: center;
  padding: 14px 30px;
  border-radius: 100px;
  line-height: 1;
  font-weight: 600;
  font-size: 16px;
  text-transform: capitalize;
  color: var(--header);
  transition: all 0.3s ease-in-out;
}
@media (max-width: 991px) {
  .pricing-tab-header .nav .nav-link {
    padding: 14px 45px;
    font-size: 18px;
  }
}
@media (max-width: 575px) {
  .pricing-tab-header .nav .nav-link {
    padding: 12px 20px;
    font-size: 14px;
  }
}
.pricing-tab-header .nav .nav-link.active {
  position: relative;
  background-color: #600ee4;
  color: var(--white);
}

.pricing-section {
  position: relative;
}
.pricing-section .shape-image {
  position: absolute;
  top: 0;
  left: 50px;
}
@media (max-width: 991px) {
  .pricing-section .shape-image {
    display: none;
  }
}
.pricing-section .shape-image-2 {
  position: absolute;
  top: 0;
  right: 5%;
  animation: rounded 5s linear infinite;
}
@media (max-width: 991px) {
  .pricing-section .shape-image-2 {
    display: none;
  }
}
.pricing-section .circle-shape {
  position: absolute;
  left: 100px;
  bottom: 25%;
  animation: cir36 10s linear infinite;
}

.portfolio-wrapper {
  border-radius: 23px 0 0 23px;
  background: linear-gradient(
    49deg,
    #600ee4 17.28%,
    #790fde 51.11%,
    #a41fe2 94.57%
  );
  position: relative;
}
@media (max-width: 1399px) {
  .portfolio-wrapper {
    padding: 50px 0 30px;
  }
}
.portfolio-wrapper .cap-shape {
  position: absolute;
  top: -60px;
  left: 10px;
}
@media (max-width: 1899px) {
  .portfolio-wrapper .cap-shape {
    display: none;
  }
}
.portfolio-wrapper .shape-2 {
  position: absolute;
  left: 70px;
  top: 20%;
}
.portfolio-wrapper .shape-3 {
  position: absolute;
  left: 70px;
  bottom: 15%;
  animation: rounded 5s linear infinite;
}
.portfolio-wrapper .portfolio-content {
  margin-left: -32px;
}
@media (max-width: 1199px) {
  .portfolio-wrapper .portfolio-content {
    margin-left: 0;
    margin-bottom: 30px;
  }
}
.portfolio-wrapper .portfolio-content .section-title h6 {
  background: rgba(255, 255, 255, 0.23);
}
.portfolio-wrapper .portfolio-content .section-title h2 {
  font-size: 60px;
  color: var(--white);
}
@media (max-width: 991px) {
  .portfolio-wrapper .portfolio-content .section-title h2 {
    font-size: 54px;
  }
}
@media (max-width: 575px) {
  .portfolio-wrapper .portfolio-content .section-title h2 {
    font-size: 40px;
  }
}
.portfolio-wrapper .portfolio-content .list-items {
  margin-bottom: 50px;
}
.portfolio-wrapper .portfolio-content .list-items li {
  color: var(--white);
  font-weight: 500;
}
.portfolio-wrapper .portfolio-content .list-items li:not(:last-child) {
  margin-bottom: 10px;
}
.portfolio-wrapper .portfolio-content .list-items li i {
  margin-right: 10px;
  color: #fc3b3b;
}
.portfolio-wrapper .portfolio-image-items {
  margin-right: -45%;
}
@media (max-width: 1899px) {
  .portfolio-wrapper .portfolio-image-items {
    margin-right: -6%;
  }
}
@media (max-width: 1199px) {
  .portfolio-wrapper .portfolio-image-items {
    margin-right: 0;
  }
}
@media (max-width: 991px) {
  .portfolio-wrapper .portfolio-image-items {
    margin-right: 0;
  }
}
.portfolio-wrapper .portfolio-image-items .portfolio-image {
  position: relative;
}
.portfolio-wrapper .portfolio-image-items .portfolio-image img {
  width: 100%;
  height: 100%;
  transition: all 0.4s ease-in-out;
}
.portfolio-wrapper .portfolio-image-items .portfolio-image .icon {
  position: absolute;
  top: 30px;
  right: 30px;
  width: 54px;
  height: 54px;
  line-height: 54px;
  border-radius: 50%;
  background-color: var(--white);
  color: var(--header);
  display: inline-block;
  text-align: center;
  transition: all 0.4s ease-in-out;
}
.portfolio-wrapper .portfolio-image-items .portfolio-image .icon:hover {
  background-color: var(--theme);
  color: var(--white);
}
.portfolio-wrapper .portfolio-image-items .portfolio-image .portfolio-content {
  position: absolute;
  top: 50%;
  inset-inline-start: 0;
  background-size: cover;
  background-repeat: no-repeat;
  background-position-x: 75%;
  opacity: 0;
  transition: opacity 0.3s, transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
  margin: -200px 0 0 -50px;
  overflow: hidden;
  z-index: 9;
  visibility: hidden;
}
.portfolio-wrapper
  .portfolio-image-items
  .portfolio-image
  .portfolio-content
  h3 {
  background: #04d493;
  padding: 15px 40px 15px 25px;
  color: var(--white);
  line-height: 1;
  font-family: "Satoshi", sans-serif;
}
.portfolio-wrapper
  .portfolio-image-items
  .portfolio-image
  .portfolio-content
  h3
  a {
  color: var(--white);
}
.portfolio-wrapper
  .portfolio-image-items
  .portfolio-image
  .portfolio-content
  h4 {
  padding: 7px 30px;
  line-height: 1;
  background: rgba(255, 255, 255, 0.3);
  color: var(--white);
  display: inline-block;
}
.portfolio-wrapper .portfolio-image-items .portfolio-image:hover {
  z-index: 9;
}
.portfolio-wrapper .portfolio-image-items .portfolio-image:hover img {
  transform: scale(1.05);
}
.portfolio-wrapper
  .portfolio-image-items
  .portfolio-image:hover
  .portfolio-content {
  opacity: 1;
  visibility: visible;
}

.portfolio-section {
  margin: 0 0 0 62px;
}
@media (max-width: 1899px) {
  .portfolio-section {
    margin: 0;
  }
}

.project-section .container-fluid {
  padding: 0 50px;
}
@media (max-width: 1600px) {
  .project-section .container-fluid {
    padding: 0 10px;
  }
}

.project-image {
  position: relative;
  margin-top: 24px;
  height: 370px;
  overflow: hidden;
}
.project-image img {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  object-fit: cover;
}
.project-image.style-2 {
  height: 535px;
}
.project-image.style-3 {
  height: 205px;
}
.project-image .portfolio-content {
  position: absolute;
  left: 30px;
  bottom: -100px;
  transition: all 0.4s ease-in-out;
  opacity: 0;
  visibility: hidden;
}
.project-image .portfolio-content h3 {
  background: #04d493;
  padding: 15px 25px;
  color: var(--white);
  line-height: 1;
  font-family: "Satoshi", sans-serif;
}
.project-image .portfolio-content h3 a {
  color: var(--white);
}
.project-image .portfolio-content h3:hover {
  background-color: var(--theme);
}
.project-image .portfolio-content h4 {
  padding: 7px 30px;
  line-height: 1;
  background: rgba(255, 255, 255, 0.3);
  color: var(--white);
  display: inline-block;
}
.project-image:hover .portfolio-content {
  bottom: 30px;
  opacity: 1;
  visibility: visible;
}

.project-wrapper-3 {
  margin-left: -300px;
  margin-right: -300px;
  position: relative;
}
@media (max-width: 1399px) {
  .project-wrapper-3 {
    margin: 0;
  }
}
.project-wrapper-3 .array-button {
  position: static;
}
@media (max-width: 1399px) {
  .project-wrapper-3 .array-button {
    display: none;
  }
}
.project-wrapper-3 .array-button .array-prev,
.project-wrapper-3 .array-button .array-next {
  width: 65px;
  height: 65px;
  line-height: 65px;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.71);
  color: var(--white);
  border: none;
}
.project-wrapper-3 .array-button .array-prev:hover,
.project-wrapper-3 .array-button .array-next:hover {
  background-color: #ff1b1b;
}
.project-wrapper-3 .array-button .array-prev {
  position: absolute;
  top: 50%;
  left: 32%;
  transform: translateY(-50%);
  z-index: 9;
}
.project-wrapper-3 .array-button .array-next {
  position: absolute;
  top: 50%;
  right: 32%;
  transform: translateY(-50%);
  z-index: 9;
}
.project-wrapper-3 .project-thumb {
  position: relative;
  overflow: hidden;
}
.project-wrapper-3 .project-thumb img {
  width: 100%;
  height: 100%;
}
.project-wrapper-3 .project-thumb .content {
  position: absolute;
  left: 50px;
  bottom: -100px;
  transition: all 0.4s ease-in-out;
  opacity: 0;
  visibility: hidden;
  display: none;
}
@media (max-width: 1399px) {
  .project-wrapper-3 .project-thumb .content {
    left: 30px;
  }
}
.project-wrapper-3 .project-thumb .content h4 {
  padding: 10px 90px 10px 20px;
  background-color: var(--white);
}
.project-wrapper-3 .project-thumb .content p {
  padding: 10px 30px 10px 20px;
  background-color: var(--theme);
  color: var(--white);
  font-size: 18px;
  display: inline-block;
}
.project-wrapper-3 .project-thumb .project-button {
  position: absolute;
  top: 50%;
  inset-inline-start: 0;
  background-size: cover;
  background-repeat: no-repeat;
  background-position-x: 75%;
  opacity: 0;
  transition: opacity 0.3s, transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
  margin: -350px 0 0 -50px;
  overflow: hidden;
  z-index: 9;
  visibility: hidden;
}
@media (max-width: 1399px) {
  .project-wrapper-3 .project-thumb .project-button {
    display: none !important;
  }
}
.project-wrapper-3 .project-thumb .project-button .btns {
  width: 170px;
  height: 170px;
  line-height: 60px;
  padding-top: 40px;
  border-radius: 50%;
  background-color: #ff1b1b;
  color: var(--white);
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  text-align: center;
  display: inline-block;
  transition: all 0.4s ease-in-out;
}
.project-wrapper-3 .project-thumb .project-button .btns i {
  transform: rotate(-40deg);
  display: block;
}
.project-wrapper-3 .project-thumb .project-button .btns:hover {
  background-color: var(--header);
}
.project-wrapper-3 .project-thumb:hover .project-button {
  opacity: 1;
  visibility: visible;
}
.project-wrapper-3 .project-thumb:hover .content {
  opacity: 1;
  visibility: visible;
  bottom: 70px;
}
@media (max-width: 1399px) {
  .project-wrapper-3 .project-thumb:hover .content {
    bottom: 30px;
  }
}

.project-box-image-2 {
  position: relative;
}
.project-box-image-2 img {
  width: 100%;
  height: 100%;
  border-radius: 15px;
}
.project-box-image-2 .content {
  position: absolute;
  left: 0;
  bottom: 0;
  padding: 28px 40px;
  background-color: var(--white);
  border-radius: 7px 7px 7px 0px;
  box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.09);
  display: flex;
  align-items: center;
  gap: 130px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease-in-out;
}
@media (max-width: 1199px) {
  .project-box-image-2 .content {
    gap: 50px;
  }
}
@media (max-width: 767px) {
  .project-box-image-2 .content {
    padding: 22px 30px;
    gap: 30px;
  }
}
.project-box-image-2 .content h4 a:hover {
  color: var(--theme);
}
.project-box-image-2 .content p {
  font-weight: 400;
  margin-top: 5px;
}
.project-box-image-2 .content .icon {
  display: inline-block;
  width: 55px;
  height: 55px;
  line-height: 55px;
  text-align: center;
  border-radius: 50%;
  border: 1px solid rgba(85, 74, 74, 0.27);
  transition: all 0.4s ease-in-out;
}
.project-box-image-2 .content .icon:hover {
  background-color: var(--theme);
  color: var(--white);
}
.project-box-image-2 .content .bar-img {
  position: absolute;
  top: 0;
  bottom: 0;
  left: -3px;
}
.project-box-image-2 .content .bar-img img {
  width: 100%;
  height: 100%;
}
.project-box-image-2.style-2 .content {
  gap: 30px;
}
.project-box-image-2:hover .content {
  opacity: 1;
  visibility: visible;
}

.project-details-wrapper p {
  font-weight: 400;
}
.project-details-wrapper .project-title-content .text {
  margin-bottom: 30px;
  font-size: 18px;
  font-weight: 500;
  font-family: "Space Grotesk", sans-serif;
  background-color: var(--theme);
  color: var(--white);
  padding: 13px 40px;
  border-radius: 50px;
  display: inline-block;
  line-height: 1;
}
.project-details-wrapper .project-title-content h2 {
  font-weight: 600;
  margin-bottom: 25px;
}
.project-details-wrapper .project-title-content p {
  margin-bottom: 30px;
}
.project-details-wrapper .project-title-content .theme-btn {
  border-radius: 12px;
  background-color: var(--theme);
}
.project-details-wrapper .project-title-content .theme-btn::before {
  background-color: var(--header);
}
.project-details-wrapper .details-info {
  padding: 50px;
  background-color: #e4eaef;
}
.project-details-wrapper .details-info .info-items h5 {
  margin-bottom: 5px;
}
.project-details-wrapper .details-info .info-items a {
  color: var(--theme);
  text-decoration: underline;
}
.project-details-wrapper .project-details-image {
  margin-top: 60px;
}
.project-details-wrapper .project-details-image img {
  width: 100%;
  height: 100%;
}
.project-details-wrapper .project-details-image .details-image {
  height: 505px;
}
.project-details-wrapper .project-details-image .details-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.project-details-wrapper .project-details-content {
  margin-top: 60px;
  padding-bottom: 60px;
  border-bottom: 1px solid rgba(2, 6, 38, 0.07);
}
.project-details-wrapper .project-details-content .details-content .list-items {
  display: flex;
  align-items: center;
  gap: 70px;
  margin-top: 30px;
}
@media (max-width: 767px) {
  .project-details-wrapper
    .project-details-content
    .details-content
    .list-items {
    gap: 30px;
    flex-wrap: wrap;
  }
}
.project-details-wrapper
  .project-details-content
  .details-content
  .list-items
  li {
  font-weight: 600;
  color: var(--header);
}
.project-details-wrapper
  .project-details-content
  .details-content
  .list-items
  li:not(:last-child) {
  margin-bottom: 10px;
}
.project-details-wrapper
  .project-details-content
  .details-content
  .list-items
  li
  i {
  margin-right: 5px;
  color: var(--theme);
}
.project-details-wrapper .project-details-content.style-2 {
  border: none;
}
.project-details-wrapper
  .project-details-content.style-2
  .details-content
  .list-items-area {
  margin-top: 30px;
}
.project-details-wrapper
  .project-details-content.style-2
  .details-content
  .list-items-area
  .icon {
  font-size: 20px;
  font-weight: 600;
  color: var(--header);
  margin-bottom: 10px;
}
.project-details-wrapper
  .project-details-content.style-2
  .details-content
  .list-items-area
  .icon
  i {
  color: var(--theme);
  font-size: 24px;
  margin-right: 10px;
}
.project-details-wrapper
  .project-details-content.style-2
  .details-content
  .list-items-area
  p {
  padding-right: 100px;
}
.project-details-wrapper .previous-button {
  margin-top: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.project-details-wrapper .previous-button .preview-button i {
  font-size: 14px;
  margin-left: 5px;
}
.project-details-wrapper .previous-button .preview-button h4 {
  margin-top: 5px;
  font-weight: 600;
}
.project-details-wrapper .previous-button .preview-button h4 a {
  background-image: linear-gradient(var(--theme), var(--theme));
  background-position: 0 95%;
  background-repeat: no-repeat;
  background-size: 0% 2px;
  transition: all 0.4s ease-in-out;
}
.project-details-wrapper .previous-button .preview-button h4 a:hover {
  color: var(--theme);
  background-size: 100% 2px;
}
.project-details-wrapper .previous-button .preview-button.style-2 {
  text-align: right;
}
.project-details-wrapper .previous-button .icon {
  font-size: 32px;
  color: var(--theme);
}

.section-title {
  position: relative;
  z-index: 99;
  margin-bottom: 18px;
}
@media (max-width: 767px) {
  .section-title {
    margin-bottom: 0;
  }
}
.section-title h6 {
  font-size: 18px;
  font-weight: 700;
  font-family: "Space Grotesk", sans-serif;
  background-color: var(--theme);
  color: var(--white);
  padding: 14px 30px;
  border-radius: 50px;
  display: inline-block;
  margin-bottom: 20px;
  line-height: 1;
}
.section-title h6.bg-2 {
  background: #c9efff;
  color: var(--header);
}
.section-title h6.bg-3 {
  background: #ffc9d6;
  color: var(--header);
}
.section-title h6.bg-4 {
  background-color: #ffd9d3;
  color: var(--header);
}
.section-title h6.bg-white {
  background-color: var(--white);
  color: var(--header);
}
.section-title h6.bg-5 {
  background-color: #fec9ff;
  color: var(--header);
}
@media (max-width: 470px) {
  .section-title h2 {
    font-size: 32px !important;
  }
}
.section-title h2 span {
  position: relative;
  z-index: 1;
  display: inline-block;
}
.section-title h2 span img {
  position: absolute;
  bottom: 10px;
  left: 0;
  z-index: -1;
}
@media (max-width: 1199px) {
  .section-title h2 span img {
    display: none;
  }
}

.section-title-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 9;
}
@media (max-width: 991px) {
  .section-title-area {
    flex-wrap: wrap;
    gap: 30px;
  }
}

.center {
  text-align: center;
  margin: 0 auto;
}

.section-bg {
  background-color: var(--bg);
}

.section-bg-2 {
  background-color: var(--bg2);
}

.section-padding {
  padding: 130px 0;
}
@media (max-width: 1199px) {
  .section-padding {
    padding: 100px 0;
  }
}
@media (max-width: 991px) {
  .section-padding {
    padding: 80px 0;
  }
}

.service-card-items {
  background: var(--white);
  text-align: center;
  margin-top: 30px;
  border-radius: 8px;
  border: 1px solid var(--white);
  transition: all 0.4s ease-in-out;
}
.service-card-items .service-cotent {
  margin-bottom: 45px;
  padding: 40px 45px 0;
}
.service-card-items .service-cotent h3 a:hover {
  color: var(--theme);
}
.service-card-items .service-cotent p {
  font-size: 20px;
  font-weight: 700;
  line-height: 160%;
  margin-top: 16px;
}
.service-card-items .service-btn {
  margin-top: 55px;
  border-top: 1px solid var(--border);
  padding: 35px 0 40px;
}
.service-card-items .service-btn .link-btn i {
  transition: all 0.4s ease-in-out;
}
.service-card-items .service-btn .link-btn:hover {
  color: var(--theme2);
}
.service-card-items .service-btn .link-btn:hover i {
  transform: rotate(0);
}
.service-card-items:hover {
  border: 1px solid var(--theme);
}

.service-wrapper {
  margin-bottom: 80px;
}
.service-wrapper.style-2 {
  margin-bottom: 0;
  margin-right: -53%;
}
@media (max-width: 575px) {
  .service-wrapper.style-2 {
    margin-right: 0;
  }
}

.service-section {
  position: relative;
}
.service-section .shape-image {
  position: absolute;
  bottom: -33.3%;
  left: 0;
}
@media (max-width: 1199px) {
  .service-section .shape-image {
    display: none;
  }
}
.service-section .shape-image-2 {
  position: absolute;
  top: 45%;
  left: 0;
  animation: rounded 5s linear infinite;
}
.service-section .shape-image-3 {
  position: absolute;
  bottom: 0;
  right: 0;
}

.service-section-2 {
  position: relative;
}
.service-section-2 .left-bg {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.service-card-items-2 {
  margin-top: 30px;
  padding: 50px;
  border-radius: 15px;
  border: 1px solid #e1e1e1;
  background: #f5f9fc;
  transition: all 0.4s ease-in-out;
  position: relative;
  z-index: 9;
}
@media (max-width: 991px) {
  .service-card-items-2 {
    padding: 50px 30px;
  }
}
@media (max-width: 767px) {
  .service-card-items-2 {
    padding: 45px 35px;
  }
}
@media (max-width: 575px) {
  .service-card-items-2 {
    padding: 40px 30px;
  }
}
.service-card-items-2 .service-content span {
  font-size: 18px;
  font-weight: 600;
  color: var(--header);
  display: inline-block;
  border-radius: 18.5px;
  background: rgba(230, 229, 246, 0.89);
  padding: 10px 25px;
  line-height: 1;
  font-family: "Space Grotesk", sans-serif;
  text-transform: capitalize;
  margin-bottom: 20px;
}
.service-card-items-2 .service-content h3 {
  font-size: 28px;
  margin-bottom: 15px;
}
.service-card-items-2 .service-content h3 a:hover {
  color: var(--theme);
}
.service-card-items-2 .service-content .link-btn {
  font-size: 16px;
  font-weight: 600;
  font-family: "Space Grotesk", sans-serif;
  margin-top: 20px;
}
.service-card-items-2 .service-content .link-btn i {
  transform: rotate(0);
}
.service-card-items-2 .service-content .link-btn:hover {
  color: var(--theme2);
}
.service-card-items-2 .service-image {
  text-align: right;
}
.service-card-items-2:hover {
  border: 1px solid var(--header);
}

.service-box-items {
  margin-top: 30px;
  padding: 50px 37px 55px;
  background-color: var(--white);
  border-radius: 10px;
  text-align: center;
}
.service-box-items h3 {
  font-size: 24px;
  border-bottom: 1px dashed #e1e1e1;
  padding-bottom: 20px;
}
.service-box-items h3 a:hover {
  color: var(--theme);
}
.service-box-items .service-image {
  margin-top: 35px;
  height: 184px;
}
.service-box-items .service-content {
  margin-top: 25px;
}
.service-box-items .service-content p {
  font-size: 18px;
  color: #6e707a;
  line-height: 178%;
  border-bottom: 1px solid #e1e1e1;
  padding-bottom: 30px;
}
.service-box-items .service-content .service-btn {
  color: var(--header);
  font-size: 16px;
  font-weight: 700;
  font-family: "Space Grotesk", sans-serif;
  display: inline-block;
  margin-top: 25px;
  transition: all 0.4s ease-in-out;
}
.service-box-items .service-content .service-btn i {
  width: 50px;
  height: 50px;
  line-height: 50px;
  border-radius: 50%;
  background-color: #f1f1f1;
  margin-left: 18px;
  transition: all 0.4s ease-in-out;
}
.service-box-items .service-content .service-btn:hover {
  color: var(--theme);
}
.service-box-items .service-content .service-btn:hover i {
  background-color: var(--theme);
  color: var(--white);
}

.services-wrapper-3 {
  padding: 0 67px;
}
@media (max-width: 1199px) {
  .services-wrapper-3 {
    padding: 0 30px;
  }
}
.services-wrapper-3 .service-box-items-3 {
  margin-top: 30px;
  position: relative;
}
.services-wrapper-3 .service-box-items-3::before {
  top: 0;
  left: 0;
  position: absolute;
  width: 50%;
  height: 100%;
  content: "";
  background-color: var(--white);
  bottom: 0;
  right: 0;
  z-index: 1;
  transition: all 0.4s ease-in-out;
}
@media (max-width: 991px) {
  .services-wrapper-3 .service-box-items-3::before {
    display: none;
  }
}
.services-wrapper-3 .service-box-items-3 .service-thumb {
  position: relative;
}
.services-wrapper-3 .service-box-items-3 .service-thumb img {
  width: 100%;
  height: 100%;
}
.services-wrapper-3 .service-box-items-3 .service-thumb .service-content {
  position: absolute;
  bottom: 85px;
  left: 40px;
  z-index: 9;
}
@media (max-width: 991px) {
  .services-wrapper-3 .service-box-items-3 .service-thumb .service-content {
    bottom: 40px;
  }
}
.services-wrapper-3 .service-box-items-3 .service-thumb .service-content h4 {
  font-size: 22px;
  color: var(--theme);
  margin-bottom: 15px;
}
.services-wrapper-3 .service-box-items-3 .service-thumb .service-content h3 {
  font-size: 24px;
  color: var(--white);
}
@media (max-width: 991px) {
  .services-wrapper-3
    .service-box-items-3
    .service-thumb
    .service-content
    h3
    a {
    color: var(--white);
  }
}
.services-wrapper-3
  .service-box-items-3
  .service-thumb
  .service-content
  h3
  a:hover {
  color: var(--theme);
}
.services-wrapper-3 .service-box-items-3 .service-thumb .service-content p {
  font-weight: 400;
  margin-top: 20px;
}
@media (max-width: 991px) {
  .services-wrapper-3 .service-box-items-3 .service-thumb .service-content p {
    color: var(--white);
  }
}
.services-wrapper-3
  .service-box-items-3
  .service-thumb
  .service-content
  .arrow-icon {
  width: 76px;
  height: 47px;
  background-color: var(--theme);
  border-radius: 23.5px;
  display: inline-block;
  text-align: center;
  line-height: 47px;
  color: var(--white);
  margin-top: 30px;
}
.services-wrapper-3 .service-box-items-3 .service-thumb .icon {
  position: absolute;
  top: 85px;
  left: 40px;
  z-index: 9;
  transition: all 0.4s ease-in-out;
}
@media (max-width: 991px) {
  .services-wrapper-3 .service-box-items-3 .service-thumb .icon {
    top: 40px;
  }
}
.services-wrapper-3 .service-box-items-3:hover::before {
  background-color: transparent;
}
.services-wrapper-3
  .service-box-items-3:hover
  .service-thumb
  .service-content
  h3
  a {
  color: var(--white);
}
.services-wrapper-3
  .service-box-items-3:hover
  .service-thumb
  .service-content
  h3
  a:hover {
  color: var(--theme);
}
.services-wrapper-3
  .service-box-items-3:hover
  .service-thumb
  .service-content
  p {
  color: var(--white);
}
.services-wrapper-3
  .service-box-items-3:hover
  .service-thumb
  .service-content
  .arrow-icon {
  background-color: var(--white);
  color: var(--header);
}
.services-wrapper-3
  .service-box-items-3:hover
  .service-thumb
  .service-content
  .arrow-icon:hover {
  color: var(--white);
  background-color: var(--theme);
}
.services-wrapper-3 .service-box-items-3:hover .service-thumb .icon {
  opacity: 0;
}

.service-details-wrapper .service-details-image img {
  width: 100%;
  height: 100%;
}
.service-details-wrapper .service-details-content {
  margin-top: 30px;
}
.service-details-wrapper .service-details-content h3 {
  font-size: 28px;
}
.service-details-wrapper .service-details-content p {
  font-weight: 400;
}
.service-details-wrapper .service-details-content .service-details-video {
  margin-top: 50px;
  margin-bottom: 40px;
}
@media (max-width: 575px) {
  .service-details-wrapper .service-details-content .service-details-video {
    margin-top: 25px;
    margin-bottom: 30px;
  }
}
@media (max-width: 575px) {
  .service-details-wrapper .service-details-content .service-details-video {
    margin-bottom: 20px;
    margin-top: 20px;
  }
}
.service-details-wrapper
  .service-details-content
  .service-details-video
  .details-video-content
  ul {
  margin-top: 20px;
}
.service-details-wrapper
  .service-details-content
  .service-details-video
  .details-video-content
  ul
  li {
  font-weight: 400;
}
.service-details-wrapper
  .service-details-content
  .service-details-video
  .details-video-content
  ul
  li:not(:last-child) {
  margin-bottom: 10px;
}
.service-details-wrapper
  .service-details-content
  .service-details-video
  .details-video-content
  ul
  li
  i {
  color: var(--theme);
  margin-right: 8px;
}
.service-details-wrapper
  .service-details-content
  .service-details-video
  .video-image {
  position: relative;
}
.service-details-wrapper
  .service-details-content
  .service-details-video
  .video-image
  img {
  width: 100%;
  height: 100%;
}
.service-details-wrapper
  .service-details-content
  .service-details-video
  .video-image
  .video-box {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.service-details-wrapper
  .service-details-content
  .service-details-video
  .video-image
  .video-box
  .video-btn {
  background-color: var(--white);
  width: 80px;
  line-height: 80px;
  height: 80px;
  text-align: center;
  color: var(--theme);
  border-radius: 50%;
  display: inline-block;
}
.service-details-wrapper
  .service-details-content
  .service-details-video
  .video-image
  .video-box
  .ripple::before,
.service-details-wrapper
  .service-details-content
  .service-details-video
  .video-image
  .video-box
  .ripple::after {
  width: 80px;
  height: 80px;
  box-shadow: 0 0 0 0 rgba(8, 189, 201, 0.6);
}
.service-details-wrapper .service-details-content .highlight-text {
  margin-top: 50px;
  padding: 18px;
  border-left: 4px solid var(--theme);
  box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.06);
  background: var(--white);
}
.service-details-wrapper .service-details-content .highlight-text h5 {
  font-weight: 600;
  line-height: 167%;
}
.service-details-wrapper .service-details-content .service-image-item {
  margin-top: 60px;
  margin-bottom: 60px;
}
.service-details-wrapper .service-details-content .service-image-item h3 {
  font-size: 24px;
}
.service-details-wrapper
  .service-details-content
  .service-image-item
  .service-box-image {
  height: 218px;
}
.service-details-wrapper
  .service-details-content
  .service-image-item
  .service-box-image
  img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.service-sidebar .service-widget-categories {
  margin-bottom: 30px;
}
.service-sidebar .service-widget-categories ul li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 22px 25px;
  background: rgba(98, 116, 255, 0.06);
  font-weight: 500;
  transition: all 0.4s ease-in-out;
  border-radius: 0;
  font-size: 18px;
  line-height: 1;
  font-weight: 700;
  font-family: "Space Grotesk", sans-serif;
}
.service-sidebar .service-widget-categories ul li a {
  color: var(--header);
}
.service-sidebar .service-widget-categories ul li span {
  transition: all 0.4s ease-in-out;
  color: var(--header);
}
.service-sidebar .service-widget-categories ul li:not(:last-child) {
  margin-bottom: 12px;
}
.service-sidebar .service-widget-categories ul li:hover {
  background-color: var(--theme);
}
.service-sidebar .service-widget-categories ul li:hover a {
  color: var(--white);
}
.service-sidebar .service-widget-categories ul li:hover span {
  color: var(--white);
}
.service-sidebar .service-widget-categories ul li.active {
  background-color: var(--theme);
}
.service-sidebar .service-widget-categories ul li.active a {
  color: var(--white);
}
.service-sidebar .service-widget-categories ul li.active span {
  color: var(--white);
}
.service-sidebar .contact-bg {
  padding: 40px 25px;
  text-align: center;
}
.service-sidebar .contact-bg .icon {
  width: 86.526px;
  height: 76px;
  text-align: center;
  line-height: 76px;
  border-radius: 4px;
  background-color: var(--theme);
  font-size: 32px;
  color: var(--white);
  margin: 0 auto 25px;
}
.service-sidebar .contact-bg h3 {
  font-size: 26px;
  color: var(--white);
  line-height: 139%;
  margin-bottom: 30px;
}
.service-sidebar .contact-bg p {
  color: var(--white);
  font-size: 15px;
  font-weight: 400;
  letter-spacing: 1.95px;
  text-transform: uppercase;
  margin-bottom: 15px;
}
.service-sidebar .contact-bg .theme-btn {
  padding: 18.5px 50px;
  background-color: var(--theme);
}
.service-sidebar .contact-bg .theme-btn::before {
  background-color: var(--header);
}

.testimonial-section {
  margin: 0 70px;
  border-radius: 60px;
  margin-bottom: 130px;
  position: relative;
}
@media (max-width: 1600px) {
  .testimonial-section {
    margin: 0;
    border-radius: 0;
    margin-bottom: 130px;
  }
}
@media (max-width: 1199px) {
  .testimonial-section {
    margin-bottom: 100px;
  }
}
@media (max-width: 991px) {
  .testimonial-section {
    margin-bottom: 80px;
  }
}
.testimonial-section .overlay-shape {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
}
.testimonial-section .array-button {
  position: absolute;
  top: 115px;
  right: 100px;
  z-index: 99;
}
@media (max-width: 991px) {
  .testimonial-section .array-button {
    top: 65px;
    z-index: 999;
  }
}
@media (max-width: 575px) {
  .testimonial-section .array-button {
    display: none;
  }
}
.testimonial-section .array-button .array-prev {
  border: 2px solid rgba(255, 255, 255, 0.16);
  color: var(--white);
}
.testimonial-section .array-button .array-prev:hover {
  background-color: var(--theme2);
  border: 2px solid var(--theme2);
}
.testimonial-section .array-button .array-next {
  border: 2px solid var(--white);
  color: var(--header);
  background-color: var(--white);
}
.testimonial-section .array-button .array-next:hover {
  background-color: var(--theme2);
  color: var(--white);
  border: 2px solid var(--theme2);
}

.testimonial-wrapper .testimonial-content {
  margin-right: 40px;
}
@media (max-width: 1199px) {
  .testimonial-wrapper .testimonial-content {
    margin-right: 0;
  }
}
.testimonial-wrapper .testimonial-content p {
  font-size: 27px;
  line-height: 182%;
  color: var(--white);
  padding-bottom: 60px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  margin-bottom: 60px;
}
@media (max-width: 1199px) {
  .testimonial-wrapper .testimonial-content p {
    font-size: 25px;
  }
}
@media (max-width: 991px) {
  .testimonial-wrapper .testimonial-content p {
    font-size: 22px;
  }
}
@media (max-width: 767px) {
  .testimonial-wrapper .testimonial-content p {
    font-size: 20px;
  }
}
@media (max-width: 575px) {
  .testimonial-wrapper .testimonial-content p {
    font-size: 18px;
    margin-bottom: 30px;
    padding-bottom: 30px;
  }
}
.testimonial-wrapper .testimonial-content .client-info {
  display: flex;
  align-items: center;
  gap: 55px;
}
@media (max-width: 575px) {
  .testimonial-wrapper .testimonial-content .client-info {
    flex-wrap: wrap;
    gap: 30px;
  }
}
.testimonial-wrapper .testimonial-content .client-info h3 {
  font-size: 22px;
  color: var(--white);
}
.testimonial-wrapper .testimonial-content .client-info h3 span {
  font-size: 14px;
  font-weight: 500;
  font-family: "Satoshi", sans-serif;
}
.testimonial-wrapper .testimonial-content .client-info .star {
  color: #ff8c22;
  border-radius: 22.5px;
  background: rgba(255, 255, 255, 0.12);
  padding: 16px 35px;
  display: inline-block;
  line-height: 1;
}
.testimonial-wrapper .testimoni-image-items {
  position: relative;
  text-align: center;
  margin-left: 60px;
}
@media (max-width: 1199px) {
  .testimonial-wrapper .testimoni-image-items {
    margin-left: 0;
  }
}
.testimonial-wrapper .testimoni-image-items::before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  content: "";
  background-color: rgba(217, 217, 217, 0.3);
  border-radius: 21px;
  top: 30%;
  height: initial;
}
.testimonial-wrapper .testimoni-image-items .testimoni-image {
  max-width: 413px;
  margin: 0 auto;
  position: relative;
  z-index: 9;
  padding: 0 25px;
}
.testimonial-wrapper .testimoni-image-items .testimoni-image img {
  width: 100%;
  height: 100%;
}
.testimonial-wrapper .testimoni-image-items .work-shape {
  position: absolute;
  bottom: -40px;
  left: 0;
  right: 0;
  z-index: 9;
  transform: rotate(-6.383deg);
}
@media (max-width: 1399px) {
  .testimonial-wrapper .testimoni-image-items .work-shape {
    display: none;
  }
}
@media (max-width: 991px) {
  .testimonial-wrapper .testimoni-image-items .work-shape {
    display: initial;
  }
}
@media (max-width: 575px) {
  .testimonial-wrapper .testimoni-image-items .work-shape {
    display: none;
  }
}
.testimonial-wrapper .testimoni-image-items .line-shape {
  position: absolute;
  bottom: 50px;
  left: 0px;
  right: 0;
}
@media (max-width: 1399px) {
  .testimonial-wrapper .testimoni-image-items .line-shape {
    display: none;
  }
}
.testimonial-wrapper .testimoni-image-items .line-shape img {
  width: 100%;
  height: 100%;
}
.testimonial-wrapper .testimoni-image-items .icon {
  width: 90px;
  height: 90px;
  line-height: 90px;
  text-align: center;
  border-radius: 50%;
  background-color: var(--white);
  color: #4e29b7;
  position: absolute;
  right: -37px;
  top: 27%;
  font-size: 32px;
}

.testimonial-box-items {
  margin-top: 30px;
  text-align: center;
  padding: 40px 85px;
  background-color: var(--white);
  border-radius: 20px;
  box-shadow: 0px 6px 44px 0px rgba(181, 181, 234, 0.2);
  position: relative;
  margin-bottom: 120px;
  margin-left: 50px;
  margin-right: 50px;
}
@media (max-width: 767px) {
  .testimonial-box-items {
    padding: 40px 30px;
    margin-left: 30px;
    margin-right: 30px;
  }
}
.testimonial-box-items .client-img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin: 0 auto 30px;
}
.testimonial-box-items p {
  font-size: 22px;
  color: #3c4256;
  line-height: 168%;
}
@media (max-width: 767px) {
  .testimonial-box-items p {
    font-size: 18px;
  }
}
.testimonial-box-items .client-content {
  margin-top: 30px;
}
.testimonial-box-items .client-content span {
  font-size: 14px;
}
.testimonial-box-items .overlay-style {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  content: "";
  border-radius: 20px;
  box-shadow: 0px 6px 44px 0px rgba(181, 181, 234, 0.2);
  background-color: var(--white);
  z-index: -1;
  top: 35px;
  left: 35px;
  width: 92%;
}
.testimonial-box-items .overlay-style-2 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  content: "";
  border-radius: 20px;
  box-shadow: 0px 6px 44px 0px rgba(181, 181, 234, 0.2);
  background-color: var(--white);
  z-index: -2;
  left: 70px;
  width: 84%;
  top: 70px;
}

.testimonial-section-2 {
  margin-bottom: -45px;
  position: relative;
}
.testimonial-section-2 .client-1 {
  position: absolute;
  left: 10%;
  top: 25%;
}
@media (max-width: 1199px) {
  .testimonial-section-2 .client-1 {
    display: none;
  }
}
.testimonial-section-2 .client-2 {
  position: absolute;
  left: 10%;
  bottom: 25%;
}
@media (max-width: 1199px) {
  .testimonial-section-2 .client-2 {
    display: none;
  }
}
.testimonial-section-2 .client-3 {
  position: absolute;
  right: 15%;
  top: 25%;
}
@media (max-width: 1199px) {
  .testimonial-section-2 .client-3 {
    display: none;
  }
}
.testimonial-section-2 .client-4 {
  position: absolute;
  right: 15%;
  bottom: 25%;
}
@media (max-width: 1199px) {
  .testimonial-section-2 .client-4 {
    display: none;
  }
}
.testimonial-section-2 .client-5 {
  position: absolute;
  right: 7%;
  bottom: 45%;
}
@media (max-width: 1199px) {
  .testimonial-section-2 .client-5 {
    display: none;
  }
}
.testimonial-section-2 .line-shape {
  position: absolute;
  top: 30%;
  left: 7%;
  z-index: -1;
}
@media (max-width: 1199px) {
  .testimonial-section-2 .line-shape {
    display: none;
  }
}
.testimonial-section-2 .line-shape-2 {
  position: absolute;
  bottom: 30%;
  right: 12%;
  z-index: -1;
}
@media (max-width: 1199px) {
  .testimonial-section-2 .line-shape-2 {
    display: none;
  }
}
.testimonial-section-2 .array-button {
  margin-bottom: 45px;
}
.testimonial-section-2 .array-button .array-prev,
.testimonial-section-2 .array-button .array-next {
  width: 50px;
  height: 50px;
  line-height: 50px;
}
.testimonial-section-2 .array-button .array-prev {
  color: #33373a;
  box-shadow: 0px 4px 13px rgba(0, 0, 0, 0.09);
  background-color: var(--white);
}
.testimonial-section-2 .array-button .array-prev:hover {
  background-color: var(--theme);
  color: var(--white);
}
.testimonial-section-2 .array-button .array-next {
  background-color: var(--theme);
}
.testimonial-section-2 .array-button .array-next:hover {
  background-color: var(--header);
  color: var(--white);
}

.testimonial-box-items-2 {
  margin-top: 30px;
  background-color: var(--white);
  border-radius: 11px;
  border: 1px solid #dbdbdb;
  box-shadow: 0px 4px 27px 0px rgba(0, 0, 0, 0.05);
  padding: 60px 55px;
}
@media (max-width: 991px) {
  .testimonial-box-items-2 {
    padding: 40px 30px;
  }
}
.testimonial-box-items-2 .clinet-info-items {
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.testimonial-box-items-2 .clinet-info-items .star {
  color: #f09815;
}
.testimonial-box-items-2 .clinet-info-items .star i.color-2 {
  color: #ded9d1 !important;
}
.testimonial-box-items-2 h3 {
  font-size: 24px;
}
@media (max-width: 991px) {
  .testimonial-box-items-2 h3 {
    font-size: 20px;
  }
}
.testimonial-box-items-2 p {
  font-size: 22px;
  color: var(--header);
  opacity: 0.8;
  line-height: 173%;
  margin-top: 30px;
}
@media (max-width: 991px) {
  .testimonial-box-items-2 p {
    font-size: 18px;
  }
}

.product-box-items {
  margin-top: 30px;
  transition: all 0.4s ease-in-out;
}
.product-box-items .product-image {
  position: relative;
  overflow: hidden;
}
.product-box-items .product-image img {
  border-radius: 15px;
  width: 100%;
  height: 100%;
}
.product-box-items .product-image .product-icon {
  gap: 14px;
  position: absolute;
  top: 15px;
  right: 15px;
}
.product-box-items .product-image .product-icon li {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 5px;
  background-color: var(--white);
  color: var(--header);
  cursor: pointer;
  transition: all 0.4s ease-in-out;
  transform: scaleX(0);
}
.product-box-items .product-image .product-icon li:hover {
  background-color: var(--theme2);
}
.product-box-items .product-image .product-icon li:hover a {
  color: var(--white);
}
.product-box-items .product-image .shop-btn {
  position: absolute;
  right: 28px;
  left: 28px;
  bottom: -100px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease-in-out;
}
.product-box-items .product-image .shop-btn .theme-btn {
  width: 100%;
  background-color: var(--theme2);
  padding: 16.5px 50px;
}
.product-box-items .product-content {
  text-align: center;
  padding: 20px 0;
  transition: all 0.4s ease-in-out;
}
.product-box-items .product-content .star {
  color: #ffa800;
  margin-bottom: 10px;
}
.product-box-items .product-content .star i.color-2 {
  color: #b6b6b6;
}
.product-box-items .product-content h6 {
  font-size: 18px;
}
.product-box-items .product-content h6 a:hover {
  color: var(--theme2);
}
.product-box-items .product-content span {
  color: var(--theme2);
  font-size: 16px;
  font-weight: 700;
  display: inline-block;
  margin-top: 10px;
}
.product-box-items:hover {
  background-color: var(--white);
  box-shadow: 0px 5px 17px 0px rgba(0, 0, 0, 0.08);
  border-radius: 15px;
}
.product-box-items:hover .product-image .product-icon li {
  -webkit-transform: scaleX(1);
  transform: scaleX(1);
}
.product-box-items:hover .product-image .shop-btn {
  bottom: 18px;
  opacity: 1;
  visibility: visible;
}
.product-box-items.active {
  background-color: var(--white);
  box-shadow: 0px 5px 17px 0px rgba(0, 0, 0, 0.08);
  border-radius: 15px;
}
.product-box-items.active .product-image .product-icon li {
  -webkit-transform: scaleX(1);
  transform: scaleX(1);
}
.product-box-items.active .product-image .shop-btn {
  bottom: 18px;
  opacity: 1;
  visibility: visible;
}
.product-box-items.style-2 .product-image .product-icon {
  gap: 10px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
}
.product-box-items.style-2 .product-image .product-icon li {
  width: 48px;
  height: 48px;
  line-height: 48px;
  border-radius: 50%;
}
.product-box-items.style-2 .product-image .post-box {
  color: var(--white);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  background-color: #f82389;
  padding: 7px 15px;
  position: absolute;
  top: 15px;
  left: 15px;
  line-height: 1;
  border-radius: 21px;
}
.product-box-items.style-2 .product-content {
  text-align: center;
  padding: 0;
  margin-top: 20px;
}
.product-box-items.style-2 .product-content .star {
  margin-bottom: 0;
  margin-top: 12px;
}
.product-box-items.style-2 .product-content .star i.color-2 {
  color: #d2d3da;
}
.product-box-items.style-2 .product-content .price {
  margin-top: 10px;
}
.product-box-items.style-2 .product-content .price li {
  color: #e51a1a;
}
.product-box-items.style-2 .product-content .price li del {
  color: #929397;
}
.product-box-items.style-2:hover {
  background-color: transparent;
  box-shadow: none;
  border-radius: 0;
}

.product-header {
  position: relative;
  z-index: 9;
}
.product-header .nav {
  justify-content: center;
  margin-bottom: 20px;
  gap: 35px;
}
.product-header .nav .nav-item .nav-link {
  font-size: 16px;
  font-weight: 700;
  color: var(--header);
  padding: 18px 30px;
  background-color: var(--white);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.07);
  border-radius: 42px;
  line-height: 1;
  text-transform: capitalize;
}
.product-header .nav .nav-item .nav-link.active {
  background-color: var(--theme2);
  color: var(--white);
}
.product-header.style-2 .nav {
  justify-content: center;
  margin-bottom: 0;
  gap: 65px;
}
@media (max-width: 1199px) {
  .product-header.style-2 .nav {
    gap: 40px;
  }
}
@media (max-width: 991px) {
  .product-header.style-2 .nav {
    gap: 25px;
    justify-content: start;
  }
}
.product-header.style-2 .nav .nav-item .nav-link {
  font-size: 16px;
  font-weight: 700;
  color: var(--header);
  padding: 0;
  background-color: transparent;
  box-shadow: none;
  border-radius: 0;
  line-height: 1;
  text-transform: uppercase;
}
.product-header.style-2 .nav .nav-item .nav-link.active {
  color: #ff1b1b;
  text-decoration: underline;
}

.product-section {
  position: relative;
}
.product-section .shape-image {
  position: absolute;
  top: 5%;
  left: 20px;
  animation: rounded 5s linear infinite;
}

.shop-banner-items {
  border-radius: 16px;
  background: #eee;
  padding: 55px 35px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 575px) {
  .shop-banner-items {
    flex-wrap: wrap;
    gap: 30px;
    padding: 40px 30px;
    justify-content: center;
    text-align: center;
  }
}
.shop-banner-items .shop-banner-content p {
  color: #fa585d;
  font-size: 12px;
  font-weight: 700;
  margin-bottom: 5px;
}
.shop-banner-items .shop-banner-content h3 {
  font-size: 32px;
  margin-bottom: 20px;
}
.shop-banner-items .shop-banner-content h4 {
  color: #f52323;
  font-size: 20px;
  font-weight: 600;
}
.shop-banner-items .shop-banner-content h4 span {
  font-size: 16px;
  font-weight: 500;
}
.shop-banner-items .shop-banner-content .theme-btn {
  background-color: var(--theme);
  padding: 18.5px 50px;
  margin-top: 25px;
}
.shop-banner-items .shop-banner-content .theme-btn::before {
  background-color: var(--header);
}
.shop-banner-items .shop-image {
  position: relative;
  text-align: center;
  z-index: 9;
}
.shop-banner-items .shop-image::before {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  content: "";
  background-color: #fa585d;
  border-radius: 50%;
  width: 240px;
  height: 240px;
  z-index: -1;
}
.shop-banner-items .shop-image img {
  transition: all 0.4s ease-in-out;
  overflow: hidden;
}
.shop-banner-items.style-2 {
  background-color: #71d3bb;
  height: 100%;
}
.shop-banner-items.style-2 .shop-banner-content p {
  color: var(--white);
}
.shop-banner-items.style-2 .shop-banner-content h3 {
  color: var(--white);
}
.shop-banner-items.style-2 .shop-banner-content h4 {
  color: var(--white);
}
.shop-banner-items.style-2 .shop-banner-content .theme-btn {
  background-color: var(--white);
  color: var(--header);
}
.shop-banner-items.style-2 .shop-banner-content .theme-btn::before {
  background-color: var(--header);
}
.shop-banner-items.style-2 .shop-banner-content .theme-btn:hover {
  color: var(--white);
}
.shop-banner-items.style-2 .shop-image {
  position: relative;
}
.shop-banner-items.style-2 .shop-image::before {
  background-color: rgba(255, 255, 255, 0.36);
}
.shop-banner-items.style-2 .shop-image .sale-text {
  position: absolute;
  left: 60px;
  bottom: -25px;
}
.shop-banner-items.style-2 .shop-image .sale-text h5 {
  color: var(--black);
  text-align: center;
  font-family: "Satoshi", sans-serif;
  font-size: 16px;
  font-weight: 700;
  letter-spacing: 0.8px;
  text-transform: uppercase;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
}
.shop-banner-items:hover .shop-image img {
  transform: scale(1.1);
}
.shop-banner-items:hover .shop-image .sale-text img {
  transform: initial;
}

.shop-box-items {
  margin-top: 30px;
  text-align: center;
}
.shop-box-items .shop-image {
  width: 185px;
  height: 185px;
  line-height: 185px;
  text-align: center;
  border-radius: 50%;
  background-color: var(--white);
  margin: 0 auto;
}
.shop-box-items .shop-content {
  margin-top: 20px;
}
.shop-box-items .shop-content h3 {
  font-size: 21px;
  margin-bottom: 5px;
}
.shop-box-items .shop-content h3 a:hover {
  color: var(--theme);
}
@media (max-width: 1199px) {
  .shop-box-items.style-2 {
    text-align: center;
  }
}
.shop-box-items.style-2 .shop-image-2 {
  max-width: 228px;
}
@media (max-width: 1199px) {
  .shop-box-items.style-2 .shop-image-2 {
    margin: 0 auto;
    max-width: 600px;
  }
}
.shop-box-items.style-2 .shop-image-2 img {
  width: 100%;
  height: 100%;
  border-radius: 14px;
}
.shop-box-items.style-2 .shop-content h3 {
  font-size: 18px;
}

.shop-section {
  position: relative;
}
.shop-section .shape-image {
  position: absolute;
  bottom: 90px;
  left: 70px;
}
@media (max-width: 1199px) {
  .shop-section .shape-image {
    display: none;
  }
}
.shop-section .shape-image-2 {
  position: absolute;
  right: 0;
  top: 0;
}
@media (max-width: 1199px) {
  .shop-section .shape-image-2 {
    display: none;
  }
}
.shop-section .array-button {
  transition: all 0.4s ease-in-out;
  opacity: 0;
  visibility: hidden;
}
.shop-section .array-button .array-prev {
  position: absolute;
  top: 50%;
  left: 10%;
  transform: translateY(-50%);
  background-color: var(--header);
}
.shop-section .array-button .array-prev:hover {
  color: var(--white);
  background-color: var(--theme);
}
.shop-section .array-button .array-next {
  position: absolute;
  top: 50%;
  right: 10%;
  transform: translateY(-50%);
  color: var(--white);
  background-color: var(--theme);
}
.shop-section .array-button .array-next:hover {
  color: var(--white);
  background-color: var(--header);
}
.shop-section:hover .array-button {
  opacity: 1;
  visibility: visible;
}

.popular-product-section {
  position: relative;
}
.popular-product-section .array-button {
  transition: all 0.4s ease-in-out;
  opacity: 0;
  visibility: hidden;
}
.popular-product-section .array-button .array-prev {
  position: absolute;
  top: 50%;
  left: 10%;
  transform: translateY(-50%);
  background-color: var(--header);
}
.popular-product-section .array-button .array-prev:hover {
  color: var(--white);
  background-color: var(--theme);
}
.popular-product-section .array-button .array-next {
  position: absolute;
  top: 50%;
  right: 10%;
  transform: translateY(-50%);
  color: var(--white);
  background-color: var(--theme);
}
.popular-product-section .array-button .array-next:hover {
  color: var(--white);
  background-color: var(--header);
}
.popular-product-section:hover .array-button {
  opacity: 1;
  visibility: visible;
}

.shop-bottom-title {
  font-size: 18px;
  font-family: "Satoshi", sans-serif;
  font-weight: 700;
  background-color: var(--white);
  border-radius: 22.5px;
  padding: 10px 40px;
  text-align: center;
  margin: 60px auto 0;
  max-width: 610px;
}
.shop-bottom-title a {
  color: var(--theme);
  text-decoration: underline;
}
.shop-bottom-title a:hover {
  color: var(--theme2);
}
.shop-bottom-title.style-2 {
  max-width: 400px;
  font-size: 16px;
}
.shop-bottom-title.style-2 span {
  color: #ff4d6d;
}

.product-demand-section {
  position: relative;
}
.product-demand-section .line-shape {
  position: absolute;
  left: 0;
  top: 30%;
}
@media (max-width: 1399px) {
  .product-demand-section .line-shape {
    display: none;
  }
}

.popular-product-section {
  position: relative;
  z-index: 9;
}
.popular-product-section::before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  content: "";
  border-radius: 10px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.4) 0%,
    rgba(237, 210, 247, 0.46) 52.29%,
    rgba(229, 227, 249, 0) 100%
  );
  z-index: -1;
  top: 50%;
  height: initial;
  left: 200px;
  width: 79%;
}

.product-demand-wrapper {
  margin-top: 40px;
}
.product-demand-wrapper .product-demand-image {
  position: relative;
}
.product-demand-wrapper .product-demand-image img {
  width: 100%;
  height: 100%;
}
.product-demand-wrapper .product-demand-image .ratting-shape {
  position: absolute;
  top: -8%;
  left: 10%;
}
.product-demand-wrapper .product-demand-image .brush-shape {
  position: absolute;
  bottom: 0;
  left: -15%;
  animation: rounded 5s linear infinite;
}
@media (max-width: 1199px) {
  .product-demand-wrapper .product-demand-image .brush-shape {
    left: 0;
  }
}
@media (max-width: 575px) {
  .product-demand-wrapper .product-demand-image .brush-shape {
    width: 150px;
  }
}
.product-demand-wrapper .product-demand-image .demand-shape {
  position: absolute;
  left: -10%;
  top: 7%;
}
@media (max-width: 1199px) {
  .product-demand-wrapper .product-demand-image .demand-shape {
    left: 0;
  }
}
.product-demand-wrapper .product-demand-image .home-demand-shape {
  position: absolute;
  bottom: -32px;
  right: -50px;
}
@media (max-width: 1199px) {
  .product-demand-wrapper .product-demand-image .home-demand-shape {
    right: 0;
  }
}
@media (max-width: 575px) {
  .product-demand-wrapper .product-demand-image .home-demand-shape {
    width: 150px;
  }
}
.product-demand-wrapper .product-demand-content {
  margin-left: 70px;
}
@media (max-width: 1199px) {
  .product-demand-wrapper .product-demand-content {
    margin-left: 0;
  }
}
.product-demand-wrapper .product-demand-content p {
  font-size: 20px;
  font-weight: 400;
}
@media (max-width: 767px) {
  .product-demand-wrapper .product-demand-content p {
    font-size: 18px;
  }
}
@media (max-width: 575px) {
  .product-demand-wrapper .product-demand-content p {
    font-size: 16px;
  }
}
.product-demand-wrapper .product-demand-content .icon-wrapper {
  margin-top: 50px;
  display: flex;
  align-items: center;
  gap: 70px;
  flex-wrap: wrap;
}
@media (max-width: 1199px) {
  .product-demand-wrapper .product-demand-content .icon-wrapper {
    gap: 20px;
  }
}
@media (max-width: 767px) {
  .product-demand-wrapper .product-demand-content .icon-wrapper {
    margin-top: 35px;
  }
}
@media (max-width: 575px) {
  .product-demand-wrapper .product-demand-content .icon-wrapper {
    margin-top: 25px;
  }
}
.product-demand-wrapper
  .product-demand-content
  .icon-wrapper
  .icon-items
  .icon {
  margin-bottom: 20px;
}
.product-demand-wrapper
  .product-demand-content
  .icon-wrapper
  .icon-items:not(:last-child) {
  padding-right: 65px;
  border-right: 1px solid #efefef;
}
@media (max-width: 1399px) {
  .product-demand-wrapper
    .product-demand-content
    .icon-wrapper
    .icon-items:not(:last-child) {
    padding-right: 0;
    border: none;
  }
}
.product-demand-wrapper .product-demand-content .icon-wrapper .icon-items h4 {
  font-weight: 500;
}
.product-demand-wrapper .product-demand-content ul {
  margin-top: 45px;
  margin-bottom: 45px;
}
@media (max-width: 767px) {
  .product-demand-wrapper .product-demand-content ul {
    margin-top: 30px;
    margin-bottom: 30px;
  }
}
.product-demand-wrapper .product-demand-content ul li {
  font-size: 20px;
  font-weight: 400;
  font-family: "Space Grotesk", sans-serif;
}
@media (max-width: 767px) {
  .product-demand-wrapper .product-demand-content ul li {
    font-size: 18px;
  }
}
@media (max-width: 575px) {
  .product-demand-wrapper .product-demand-content ul li {
    font-size: 16px;
  }
}
.product-demand-wrapper .product-demand-content ul li:not(:last-child) {
  margin-bottom: 20px;
}
.product-demand-wrapper .product-demand-content ul li i {
  color: #ff1b1b;
  margin-right: 15px;
}

.shop-banner-items-2 {
  background: #eee1d1;
  padding: 30px 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 1600px) {
  .shop-banner-items-2 {
    padding: 30px 30px;
  }
}
@media (max-width: 1399px) {
  .shop-banner-items-2 {
    flex-wrap: wrap;
    gap: 30px;
  }
}
@media (max-width: 1199px) {
  .shop-banner-items-2 {
    flex-wrap: initial;
    gap: initial;
    padding: 30px 60px;
  }
}
@media (max-width: 767px) {
  .shop-banner-items-2 {
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
    text-align: center;
  }
}
.shop-banner-items-2 .content {
  position: relative;
  z-index: 9;
}
.shop-banner-items-2 .content p {
  color: #ff9007;
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 0.6px;
  text-transform: uppercase;
  margin-bottom: 5px;
}
.shop-banner-items-2 .content h2 {
  font-size: 40px;
}
.shop-banner-items-2 .content h3 {
  font-size: 24px;
  font-family: "Satoshi", sans-serif;
  color: #ff8617;
  margin-top: 20px;
}
.shop-banner-items-2 .content h3 span {
  font-size: 18px;
  color: var(--black);
  margin-right: 5px;
}
.shop-banner-items-2 .content .theme-btn {
  margin-top: 35px;
  background-color: var(--white);
  color: var(--header);
}
.shop-banner-items-2 .content .theme-btn:hover {
  color: var(--white);
}
.shop-banner-items-2 .thumb {
  position: relative;
  z-index: 9;
}
.shop-banner-items-2 .thumb::before {
  position: absolute;
  top: 50%;
  left: 40%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 300px;
  border-radius: 300px;
  background: rgba(255, 255, 255, 0.8);
  content: "";
  z-index: -1;
}
.shop-banner-items-2 .thumb img {
  transition: all 0.4s ease-in-out;
}
.shop-banner-items-2 .thumb .flower-shape {
  position: absolute;
  bottom: 0;
  left: -70px;
  z-index: -1;
}
.shop-banner-items-2:hover .thumb img {
  transform: scale(1.1);
}
.shop-banner-items-2.style-2 {
  background: #c8e1ef;
  position: relative;
  height: 100%;
}
@media (max-width: 1199px) {
  .shop-banner-items-2.style-2 {
    padding: 70px 60px;
  }
}
@media (max-width: 767px) {
  .shop-banner-items-2.style-2 {
    padding: 30px 60px;
  }
}
.shop-banner-items-2.style-2 .content ul {
  margin-top: 35px;
}
@media (max-width: 767px) {
  .shop-banner-items-2.style-2 .content ul {
    margin-top: 30px;
    text-align: left;
  }
}
.shop-banner-items-2.style-2 .content ul li {
  font-size: 18px;
  font-weight: 700;
  color: var(--black);
  display: flex;
  align-items: center;
  gap: 15px;
}
.shop-banner-items-2.style-2 .content ul li span {
  width: 35px;
  height: 35px;
  line-height: 30px;
  border-radius: 50%;
  text-align: center;
  background-color: var(--white);
  display: inline-block;
}
@media (max-width: 470px) {
  .shop-banner-items-2.style-2 .content ul li {
    font-size: 14px;
  }
}
.shop-banner-items-2.style-2 .content ul li:not(:last-child) {
  margin-bottom: 16px;
}
.shop-banner-items-2.style-2 .content ul li i {
  width: 35px;
  height: 35px;
  line-height: 35px;
  text-align: center;
  border-radius: 50%;
  color: #ff1b1b;
  background-color: var(--white);
  margin-right: 15px;
}
@media (max-width: 470px) {
  .shop-banner-items-2.style-2 .content ul li i {
    margin-right: 8px;
  }
}
.shop-banner-items-2.style-2 .thumb-2 {
  position: absolute;
  right: 0;
  top: 0;
  overflow: hidden;
}
@media (max-width: 991px) {
  .shop-banner-items-2.style-2 .thumb-2 {
    display: none;
  }
}
.shop-banner-items-2.style-2 .thumb-2 img {
  transition: all 0.4s ease-in-out;
}
.shop-banner-items-2.style-2:hover .thumb-2 img {
  transform: scale(1.1);
}

.feature-product-items-2 {
  margin-top: 30px;
  position: relative;
  z-index: 9;
}
.feature-product-items-2::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 65%;
  border-radius: 14px;
  background-color: var(--white);
  transition: all 0.35s ease-in-out;
  z-index: -1;
  box-shadow: 0px 6px 44px 0px rgba(0, 0, 0, 0.07);
}
@media (max-width: 575px) {
  .feature-product-items-2::before {
    height: 60%;
  }
}
.feature-product-items-2 .product-image {
  padding: 10px 10px 0 10px;
  position: relative;
}
.feature-product-items-2 .product-image img {
  width: 100%;
  height: 100%;
  border-radius: 9px;
}
.feature-product-items-2 .product-image .product-icon {
  gap: 14px;
  position: absolute;
  top: 25px;
  right: 25px;
}
.feature-product-items-2 .product-image .product-icon li {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 5px;
  background-color: var(--white);
  color: var(--header);
  cursor: pointer;
  transition: all 0.4s ease-in-out;
  transform: scaleX(0);
  border-radius: 50%;
}
.feature-product-items-2 .product-image .product-icon li:hover {
  background-color: var(--theme);
}
.feature-product-items-2 .product-image .product-icon li:hover a {
  color: var(--white);
}
.feature-product-items-2 .product-content {
  text-align: center;
  padding: 30px 20px;
}
.feature-product-items-2 .product-content .price-list {
  display: flex;
  align-items: center;
  gap: 15px;
  justify-content: center;
  margin-bottom: 15px;
}
.feature-product-items-2 .product-content .price-list li {
  font-size: 16px;
  font-weight: 500;
  color: var(--text);
}
.feature-product-items-2 .product-content .price-list li span {
  font-size: 14px;
  background: #f0f2fc;
  color: var(--header);
  border-radius: 3px;
  padding: 7px 10px;
}
.feature-product-items-2 .product-content .price-list li:nth-child(2) {
  color: #d12525;
}
.feature-product-items-2 .product-content .dot-list {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 7px;
  margin-top: 15px;
}
.feature-product-items-2 .product-content .dot-list li {
  width: 14px;
  height: 14px;
  border-radius: 114px;
  background: #edba60;
}
.feature-product-items-2 .product-content .dot-list li:nth-child(2) {
  background-color: #8cc63f;
  position: relative;
}
.feature-product-items-2 .product-content .dot-list li:nth-child(2)::before {
  position: absolute;
  top: 50%;
  left: 50%;
  content: "";
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid var(--black);
}
.feature-product-items-2 .product-content .dot-list li:nth-child(3) {
  background-color: #009245;
}
.feature-product-items-2 .product-content h4 a:hover {
  color: var(--theme);
}
.feature-product-items-2:hover::before {
  height: 100%;
  background-color: var(--white);
}
.feature-product-items-2:hover .product-image .product-icon li {
  -webkit-transform: scaleX(1);
  transform: scaleX(1);
}

.feature-product-section-3 {
  position: relative;
}
.feature-product-section-3 .right-shape {
  position: absolute;
  top: 0;
  right: 0;
  animation: rounded 5s linear infinite;
}

.shop-main-sidebar .single-sidebar-widget {
  background-color: var(--white);
  border-radius: 9px;
  box-shadow: 0px 4px 21px 0px rgba(0, 0, 0, 0.02);
  padding: 40px 30px;
}
.shop-main-sidebar .single-sidebar-widget:not(:last-child) {
  margin-bottom: 30px;
}
@media (max-width: 575px) {
  .shop-main-sidebar .single-sidebar-widget {
    margin-bottom: 25px;
  }
}
.shop-main-sidebar .single-sidebar-widget .wid-title {
  margin-bottom: 20px;
  padding-left: 10px;
  position: relative;
}
.shop-main-sidebar .single-sidebar-widget .wid-title::before {
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  content: "";
  width: 2px;
  height: 21px;
  position: absolute;
  background-color: #ff1b1b;
}
.shop-main-sidebar .single-sidebar-widget .wid-title h4 {
  font-size: 20px;
  font-weight: 700;
}
.shop-main-sidebar .single-sidebar-widget .search_widget form {
  width: 100%;
  position: relative;
}
.shop-main-sidebar .single-sidebar-widget .search_widget form input {
  background-color: transparent;
  font-size: 15px;
  width: 100%;
  border: none;
  text-transform: capitalize;
  color: #9d9e9f;
  border-radius: 7px;
  border: 1px solid #ebecf1;
  padding: 12px 22px;
}
.shop-main-sidebar .single-sidebar-widget .search_widget form button {
  position: absolute;
  right: 24px;
  top: 12px;
  font-size: 18px;
  color: var(--header);
  text-align: center;
  transition: all 0.3s ease-in-out;
}
.shop-main-sidebar .single-sidebar-widget .shop-catagory-items ul li {
  transition: all 0.4s ease-in-out;
}
.shop-main-sidebar
  .single-sidebar-widget
  .shop-catagory-items
  ul
  li:not(:last-child) {
  margin-bottom: 20px;
}
.shop-main-sidebar .single-sidebar-widget .shop-catagory-items ul li a {
  font-weight: 500;
  color: var(--header);
  text-transform: capitalize;
}
.shop-main-sidebar .single-sidebar-widget .shop-catagory-items ul li a i {
  margin-right: 5px;
  color: #615f5d;
}
.shop-main-sidebar .single-sidebar-widget .shop-catagory-items ul li:hover {
  margin-left: 5px;
}
.shop-main-sidebar .single-sidebar-widget .shop-catagory-items ul li:hover a {
  color: var(--theme);
}
.shop-main-sidebar .single-sidebar-widget .shop-catagory-items ul li:hover a i {
  color: var(--theme);
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .price-input {
  margin-top: 30px;
  position: relative;
}
.shop-main-sidebar
  .single-sidebar-widget
  .range__barcustom
  .price-input
  .field {
  display: flex;
  align-items: center;
  font-size: 16px;
  width: 24%;
}
.shop-main-sidebar
  .single-sidebar-widget
  .range__barcustom
  .price-input
  .field
  span {
  font-size: 16px;
  font-weight: 500;
  color: var(--header);
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .separators {
  margin-left: 0;
  padding-right: 12px;
  font-size: 24px;
  line-height: 42px;
  font-weight: 500;
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .field input {
  height: 100%;
  outline: none;
  background: transparent;
  border: unset;
  font-size: 16px;
  font-weight: 500;
  color: var(--header);
  padding: 0;
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .field input span {
  font-weight: 500;
  color: var(--header);
  font-size: 16px;
}
.shop-main-sidebar
  .single-sidebar-widget
  .range__barcustom
  input[type="number"]::-webkit-outer-spin-button,
.shop-main-sidebar
  .single-sidebar-widget
  .range__barcustom
  input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
.shop-main-sidebar
  .single-sidebar-widget
  .range__barcustom
  .price-input
  .separator {
  font-size: 14px;
  font-weight: 400;
  color: var(--white);
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .slider {
  height: 6.75px;
  position: relative;
  background: #e3e5f1;
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .slider .progress {
  height: 100%;
  left: 25%;
  right: 25%;
  position: absolute;
  border-radius: 5px;
  background: var(--theme);
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .range-input {
  position: relative;
  display: flex;
  justify-content: center;
}
.shop-main-sidebar .single-sidebar-widget .range__barcustom .range-input input {
  position: absolute;
  width: 100%;
  height: 6.75px;
  top: -7px;
  background: none;
  pointer-events: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  padding: 0;
  outline: none;
}
.shop-main-sidebar
  .single-sidebar-widget
  .range__barcustom
  input[type="range"]::-webkit-slider-thumb {
  height: 17px;
  width: 7px;
  border-radius: 5px;
  background: var(--theme);
  border: 1.125px solid var(--theme);
  pointer-events: auto;
  -webkit-appearance: none;
}
.shop-main-sidebar
  .single-sidebar-widget
  .range__barcustom
  .price-input
  .theme-btn {
  padding: 14px 30px;
  border-radius: 0;
  min-width: initial;
  background-color: var(--theme);
}
.shop-main-sidebar
  .single-sidebar-widget
  .range__barcustom
  .price-input
  .theme-btn::before {
  background-color: var(--header);
}
.shop-main-sidebar
  .single-sidebar-widget
  .filter-size
  .input-save:not(:last-child) {
  margin-bottom: 20px;
}
.shop-main-sidebar .single-sidebar-widget .filter-size .input-save input {
  width: 20px;
  height: 19px;
  background-color: var(--theme);
  outline: none;
  color: var(--theme2);
  padding: 5px;
  border-radius: 4px;
}
.shop-main-sidebar .single-sidebar-widget .filter-size .input-save label {
  margin-left: 20px;
  color: var(--header);
  text-transform: capitalize;
  font-weight: 600;
  text-transform: capitalize;
}
.shop-main-sidebar .single-sidebar-widget .filter-size .checkbox-single {
  position: relative;
  padding-left: 18px;
  cursor: pointer;
  display: inherit;
}
.shop-main-sidebar .single-sidebar-widget .filter-size .checkbox-single input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
.shop-main-sidebar
  .single-sidebar-widget
  .filter-size
  .checkbox-single:not(:last-child) {
  margin-bottom: 20px;
}
.shop-main-sidebar
  .single-sidebar-widget
  .filter-size
  .checkbox-single
  .checkbox-area
  .checkmark {
  position: absolute;
  left: 0;
  height: 20px;
  width: 20px;
  border: 1px solid #e7eaf3;
  top: 5px;
  background-color: var(--white);
  border-radius: 3px;
}
.shop-main-sidebar
  .single-sidebar-widget
  .filter-size
  .checkbox-single
  .checkbox-area
  .checkmark::after {
  content: "";
}
.shop-main-sidebar
  .single-sidebar-widget
  .filter-size
  .checkbox-single
  .checkbox-area
  input:checked
  ~ .checkmark {
  background-color: var(--theme);
}
.shop-main-sidebar
  .single-sidebar-widget
  .filter-size
  .checkbox-single
  .checkbox-area
  input:checked
  ~ .checkmark::after {
  content: "\f00c";
  position: absolute;
  font-family: "Font Awesome 6 Pro";
  top: -5px;
  left: 5px;
  color: var(--theme);
  font-size: 11px;
}
.shop-main-sidebar
  .single-sidebar-widget
  .filter-size
  .checkbox-single
  .checkbox-area
  input:checked
  ~ .checkmark:after {
  display: block;
  color: var(--white);
}
.shop-main-sidebar
  .single-sidebar-widget
  .filter-size
  .checkbox-single
  .text-color {
  font-weight: 500;
  color: var(--header);
  font-size: 16px;
}
.shop-main-sidebar
  .single-sidebar-widget
  .filter-size
  .checkbox-single
  .text-color
  .star {
  color: #ffa800;
  margin-right: 5px;
}
.shop-main-sidebar
  .single-sidebar-widget
  .filter-size
  .checkbox-single
  .text-color
  .star
  i.color-2 {
  color: #e1e2e7;
}
.shop-main-sidebar .single-sidebar-widget .color-list {
  display: flex;
  align-items: center;
  gap: 15px;
}
.shop-main-sidebar .single-sidebar-widget .color-list li {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background-color: #c4c4c4;
  cursor: pointer;
}
.shop-main-sidebar .single-sidebar-widget .color-list li:nth-child(2) {
  background-color: #ed0707;
}
.shop-main-sidebar .single-sidebar-widget .color-list li:nth-child(3) {
  background-color: #00a651;
}
.shop-main-sidebar .single-sidebar-widget .color-list li:nth-child(4) {
  background-color: #fee496;
}
.shop-main-sidebar .single-sidebar-widget .color-list li:nth-child(5) {
  background-color: #2588bf;
}
.shop-main-sidebar .single-sidebar-widget .color-list li:nth-child(6) {
  background-color: #000000;
}
.shop-main-sidebar .single-sidebar-widget .shop-widget-tag span {
  color: #77787b;
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
  display: inline-block;
  padding: 0 15px;
  height: 36px;
  line-height: 36px;
  background-color: transparent;
  border-radius: 30px;
  margin-bottom: 15px;
  margin-right: 3px;
  transition: 0.3s;
  border: 1px solid #eff2f6;
}
.shop-main-sidebar .single-sidebar-widget .shop-widget-tag span:hover {
  background-color: var(--theme2);
  color: var(--white);
}

.woocommerce-notices-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #edeef3;
  padding: 0 20px;
  position: relative;
  z-index: 9;
}
@media (max-width: 767px) {
  .woocommerce-notices-wrapper {
    flex-wrap: wrap;
    padding: 20px 15px;
    gap: 20px;
    justify-content: center;
  }
}
.woocommerce-notices-wrapper p {
  font-weight: 600;
  font-size: 16px;
  color: var(--header);
}
.woocommerce-notices-wrapper p span {
  color: var(--theme);
}
.woocommerce-notices-wrapper .form-clt {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  z-index: 99;
}
.woocommerce-notices-wrapper .form-clt .nice-select {
  padding: 18px 20px;
  font-size: 16px;
  text-transform: capitalize;
  border-radius: 0;
  background-color: transparent;
  font-weight: 500;
}
@media (max-width: 767px) {
  .woocommerce-notices-wrapper .form-clt .nice-select {
    padding: 0 20px;
  }
}
.woocommerce-notices-wrapper .form-clt .nice-select::after {
  border-bottom: 1px solid var(--text);
  border-right: 1px solid var(--text);
  height: 10px;
  width: 10px;
  right: -5px;
  top: 30px;
}
@media (max-width: 767px) {
  .woocommerce-notices-wrapper .form-clt .nice-select::after {
    top: 15px;
  }
}
.woocommerce-notices-wrapper .form-clt .nice-select .list {
  right: -80px;
  background-color: var(--bg);
  width: 220px;
  padding: 12px 20px;
  border-radius: 0;
}
.woocommerce-notices-wrapper .form-clt .nice-select .option {
  border: none;
  font-size: 14px;
}
.woocommerce-notices-wrapper .form-clt .nice-select span {
  color: var(--text);
}
.woocommerce-notices-wrapper .form-clt .icon {
  margin-left: 10px;
}
.woocommerce-notices-wrapper .form-clt .icon a {
  color: var(--header);
}
.woocommerce-notices-wrapper .form-clt .icon-2 a {
  color: var(--theme);
}

.product-details-wrapper
  .product-image-items
  .tab-content
  .tab-pane
  .product-image {
  position: relative;
}
.product-details-wrapper
  .product-image-items
  .tab-content
  .tab-pane
  .product-image
  img {
  width: 100%;
  height: 100%;
  border-radius: 15px;
}
.product-details-wrapper
  .product-image-items
  .tab-content
  .tab-pane
  .product-image
  .icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 130px;
  height: 130px;
  line-height: 130px;
  border-radius: 50%;
  background-color: var(--theme);
  text-align: center;
  color: var(--white);
  font-size: 24px;
}
@media (max-width: 991px) {
  .product-details-wrapper
    .product-image-items
    .tab-content
    .tab-pane
    .product-image
    .icon {
    width: 90px;
    height: 90px;
    line-height: 90px;
    font-size: 16px;
  }
}
@media (max-width: 575px) {
  .product-details-wrapper
    .product-image-items
    .tab-content
    .tab-pane
    .product-image
    .icon {
    width: 70px;
    height: 70px;
    line-height: 70px;
    font-size: 15px;
  }
}
.product-details-wrapper .product-image-items .nav {
  border: unset;
  display: flex;
  align-items: center;
  margin-top: 20px;
  gap: 15px;
}
.product-details-wrapper .product-image-items .nav .nav-link {
  padding: 0;
  border: unset;
  background: none;
}
.product-details-wrapper .product-image-items .nav .nav-link img {
  width: 100%;
  height: 100%;
  border-radius: 5px;
}
.product-details-wrapper p {
  font-weight: 400;
}
.product-details-wrapper .product-details-content {
  margin-left: 60px;
}
@media (max-width: 1399px) {
  .product-details-wrapper .product-details-content {
    margin-left: 30px;
  }
}
@media (max-width: 991px) {
  .product-details-wrapper .product-details-content {
    margin-left: 0;
  }
}
.product-details-wrapper .product-details-content .star a {
  color: #ffb936;
  font-size: 16px;
  font-weight: 600;
}
.product-details-wrapper .product-details-content .star a.color-bg {
  color: #5c5c5b;
  opacity: 0.2;
}
.product-details-wrapper .product-details-content .star a.text-color {
  color: var(--header);
}
.product-details-wrapper .product-details-content .star span {
  background-color: #ff1b1b;
  font-weight: 600;
  color: var(--white);
  padding: 5px 8px;
  border-radius: 3px;
  margin-right: 10px;
}
.product-details-wrapper .product-details-content h3 {
  font-size: 44px;
  font-weight: 700;
}
.product-details-wrapper .product-details-content p {
  font-weight: 400;
}
.product-details-wrapper .product-details-content .price-list {
  gap: 20px;
}
.product-details-wrapper .product-details-content .price-list span {
  font-weight: 600;
  font-size: 28px;
  color: var(--header);
}
.product-details-wrapper .product-details-content .price-list del {
  font-size: 20px;
  color: #d12525;
  font-weight: 500;
}
.product-details-wrapper .product-details-content .price-list h3 {
  font-size: 40px;
  color: var(--theme2);
}
.product-details-wrapper .product-details-content .color-list {
  font-size: 18px;
  font-weight: 700;
  color: var(--header);
  font-family: "Space Grotesk", sans-serif;
  display: flex;
  align-items: center;
  gap: 20px;
  margin-top: 30px;
}
.product-details-wrapper .product-details-content .color-list .color-box {
  display: flex;
  align-items: center;
  gap: 14px;
}
.product-details-wrapper .product-details-content .color-list .color-box li {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: #c4c4c4;
  position: relative;
}
.product-details-wrapper
  .product-details-content
  .color-list
  .color-box
  li::before {
  position: absolute;
  top: 50%;
  left: 50%;
  content: "";
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #e5e5e5;
}
.product-details-wrapper
  .product-details-content
  .color-list
  .color-box
  li:nth-child(2) {
  background-color: #0b5fb5;
}
.product-details-wrapper
  .product-details-content
  .color-list
  .color-box
  li:nth-child(3) {
  background-color: #00a651;
}
.product-details-wrapper
  .product-details-content
  .color-list
  .color-box
  li:nth-child(4) {
  background-color: #fee496;
}
.product-details-wrapper
  .product-details-content
  .color-list
  .color-box
  li:nth-child(5) {
  background-color: #bc25bf;
}
.product-details-wrapper .product-details-content .color-list .size-list {
  display: flex;
  align-items: center;
  gap: 15px;
}
.product-details-wrapper .product-details-content .color-list .size-list li {
  padding: 4px 13px;
  background-color: transparent;
  color: var(--header);
  font-weight: 500;
  border: 1px solid #e8e8e8;
  transition: all 0.4s ease-in-out;
}
.product-details-wrapper
  .product-details-content
  .color-list
  .size-list
  li:hover {
  background-color: var(--theme);
  color: var(--white);
}
.product-details-wrapper .product-details-content .cart-wrp {
  margin-top: 35px;
  margin-bottom: 35px;
}
.product-details-wrapper .product-details-content .cart-wrp .cart-quantity {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 40px;
}
.product-details-wrapper .product-details-content .cart-wrp .cart-quantity h5 {
  font-weight: 700;
  font-size: 18px;
}
.product-details-wrapper
  .product-details-content
  .cart-wrp
  .cart-quantity
  .qty {
  display: flex;
  align-items: center;
  border: 1px solid var(--header);
  padding: 10px 20px;
  line-height: 1;
  justify-content: space-between;
  border-radius: 6px;
}
.product-details-wrapper
  .product-details-content
  .cart-wrp
  .cart-quantity
  .qty
  button,
.product-details-wrapper
  .product-details-content
  .cart-wrp
  .cart-quantity
  .qty
  input {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--header);
  font-weight: 400;
  font-size: 22px;
}
.product-details-wrapper
  .product-details-content
  .cart-wrp
  .cart-quantity
  .qty
  input {
  background-color: transparent;
  text-align: center;
  border-radius: 0;
  border: unset;
  outline: none;
}
.product-details-wrapper .product-details-content .cart-wrp .shop-button {
  justify-content: space-between;
  gap: 30px;
}
@media (max-width: 1199px) {
  .product-details-wrapper .product-details-content .cart-wrp .shop-button {
    flex-wrap: wrap;
    gap: 20px;
  }
}
.product-details-wrapper
  .product-details-content
  .cart-wrp
  .shop-button
  .theme-btn {
  width: 88%;
  border-radius: 30px;
  background-color: var(--theme);
}
.product-details-wrapper
  .product-details-content
  .cart-wrp
  .shop-button
  .theme-btn
  i {
  margin-right: 10px;
}
.product-details-wrapper
  .product-details-content
  .cart-wrp
  .shop-button
  .theme-btn::before {
  background-color: var(--header);
}
.product-details-wrapper
  .product-details-content
  .cart-wrp
  .shop-button
  .star-icon {
  width: 60px;
  height: 60px;
  line-height: 60px;
  border-radius: 36px;
  background-color: var(--white);
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.08);
  font-size: 20px;
  display: inline-block;
  text-align: center;
  transition: all 0.4s ease-in-out;
}
.product-details-wrapper
  .product-details-content
  .cart-wrp
  .shop-button
  .star-icon
  i {
  color: var(--header);
  transition: all 0.4s ease-in-out;
}
.product-details-wrapper
  .product-details-content
  .cart-wrp
  .shop-button
  .star-icon:hover {
  background-color: var(--theme);
}
.product-details-wrapper
  .product-details-content
  .cart-wrp
  .shop-button
  .star-icon:hover
  i {
  color: var(--white);
}
.product-details-wrapper .product-details-content .shop-text {
  font-weight: 500;
  margin-bottom: 30px;
  text-transform: capitalize;
  font-family: "Satoshi", sans-serif;
}
.product-details-wrapper .product-details-content .shop-text span {
  font-weight: 600;
}
.product-details-wrapper .product-details-content .details-info {
  position: relative;
}
.product-details-wrapper
  .product-details-content
  .details-info:not(:last-child) {
  margin-bottom: 10px;
}
.product-details-wrapper .product-details-content .details-info span {
  font-size: 18px;
  font-weight: 600;
  text-transform: capitalize;
  position: absolute;
  left: 0;
  top: 2px;
  font-family: "Satoshi", sans-serif;
}
.product-details-wrapper .product-details-content .details-info a {
  font-size: 18px;
  font-weight: 500;
  color: var(--header);
  margin-left: 120px;
  text-transform: capitalize;
  font-family: "Satoshi", sans-serif;
}
.product-details-wrapper .single-tab {
  padding-top: 80px;
}
@media (max-width: 575px) {
  .product-details-wrapper .single-tab {
    padding-top: 50px;
  }
}
.product-details-wrapper .single-tab .nav {
  padding-bottom: 20px;
  gap: 20px;
}
.product-details-wrapper .single-tab .nav .nav-link {
  text-align: center;
  padding: 18px 50px;
  background-color: #f7f7f7;
  border-radius: 5px;
  font-weight: 700;
  font-size: 18px;
  font-family: "Space Grotesk", sans-serif;
  text-transform: uppercase;
  color: var(--header);
  margin-right: 20px;
  transition: all 0.3s ease-in-out;
}
@media (max-width: 991px) {
  .product-details-wrapper .single-tab .nav .nav-link {
    padding: 14px 45px;
    font-size: 18px;
  }
}
.product-details-wrapper .single-tab .nav .nav-link.active {
  position: relative;
  background-color: var(--theme);
  color: var(--white);
}
.product-details-wrapper .single-tab .description-items .description-content {
  margin-right: 50px;
}
@media (max-width: 1399px) {
  .product-details-wrapper .single-tab .description-items .description-content {
    margin-right: 30px;
  }
}
@media (max-width: 991px) {
  .product-details-wrapper .single-tab .description-items .description-content {
    margin-right: 0;
  }
}
.product-details-wrapper
  .single-tab
  .description-items
  .description-content
  h3 {
  margin-bottom: 15px;
  font-size: 28px;
}
.product-details-wrapper
  .single-tab
  .description-items
  .description-content
  .description-list-items {
  margin-top: 20px;
  gap: 100px;
}
@media (max-width: 1199px) {
  .product-details-wrapper
    .single-tab
    .description-items
    .description-content
    .description-list-items {
    flex-wrap: wrap;
    gap: 30px;
  }
}
.product-details-wrapper
  .single-tab
  .description-items
  .description-content
  .description-list-items
  .description-list
  li {
  font-size: 16px;
  font-weight: 400;
  font-weight: 500;
}
.product-details-wrapper
  .single-tab
  .description-items
  .description-content
  .description-list-items
  .description-list
  li:not(:last-child) {
  margin-bottom: 15px;
}
.product-details-wrapper
  .single-tab
  .description-items
  .description-content
  .description-list-items
  .description-list
  li
  i {
  color: var(--theme);
  margin-right: 10px;
  font-weight: 600;
}
.product-details-wrapper
  .single-tab
  .description-items
  .description-content
  .description-list-items
  .description-list
  li
  span {
  color: var(--text);
  font-size: 16px;
  font-weight: 400;
}
@media (max-width: 767px) {
  .product-details-wrapper .single-tab .review-items .admin-items {
    justify-content: center;
    gap: 30px;
  }
}
.product-details-wrapper .single-tab .review-items .admin-items .admin-img {
  width: 100px;
  height: 100px;
}
.product-details-wrapper .single-tab .review-items .admin-items .admin-img img {
  border-radius: 50%;
}
.product-details-wrapper .single-tab .review-items .admin-items .content {
  position: relative;
  border: 1px solid var(--border);
}
.product-details-wrapper
  .single-tab
  .review-items
  .admin-items
  .content::before {
  position: absolute;
  content: "";
  width: 30px;
  height: 30px;
  background-color: var(--white);
  border-left: 1px solid var(--border);
  border-bottom: 1px solid var(--border);
  transform: rotate(45deg);
  top: 40%;
  left: -16px;
}
@media (max-width: 575px) {
  .product-details-wrapper
    .single-tab
    .review-items
    .admin-items
    .content::before {
    display: none;
  }
}
.product-details-wrapper
  .single-tab
  .review-items
  .admin-items
  .content
  .head-content
  h5
  span {
  font-size: 16px;
  margin-left: 30px;
}
.product-details-wrapper
  .single-tab
  .review-items
  .admin-items
  .content
  .star
  i {
  font-size: 16px;
  color: #ff9200;
}
.product-details-wrapper .single-tab .review-title .rate-now {
  margin-top: 15px;
  gap: 15px;
}
.product-details-wrapper .single-tab .review-title .rate-now p {
  font-size: 16px;
  text-transform: uppercase;
}
.product-details-wrapper .single-tab .review-title .rate-now i {
  font-size: 16px;
  color: #ff9200;
}
.product-details-wrapper .single-tab .review-form {
  margin-top: 40px;
}
.product-details-wrapper .single-tab .review-form .form-clt input {
  width: 100%;
  outline: none;
  border: none;
  padding: 18px 35px;
  color: var(--text);
  background-color: rgb(245, 245, 245);
  font-size: 16px;
  font-weight: 500;
  text-transform: capitalize;
}
@media (max-width: 575px) {
  .product-details-wrapper .single-tab .review-form .form-clt input {
    padding: 15px 20px;
  }
}
.product-details-wrapper .single-tab .review-form .form-clt-big textarea {
  padding: 18px 35px 170px;
  width: 100%;
  outline: none;
  color: var(--text);
  background-color: rgb(245, 245, 245);
  font-size: 16px;
  font-weight: 500;
  border: none;
  text-transform: capitalize;
}
@media (max-width: 575px) {
  .product-details-wrapper .single-tab .review-form .form-clt-big textarea {
    padding: 15px 20px;
  }
}

.main-cart-wrapper {
  border-radius: 5px;
}
.main-cart-wrapper .cart-wrapper {
  box-shadow: 0px 6px 30px rgba(0, 0, 0, 0.06);
  padding: 40px 40px;
}
.main-cart-wrapper .cart-wrapper .theme-btn {
  border-radius: 0;
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table {
  width: 100%;
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table thead {
  border-bottom: 1px solid var(--border);
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table thead tr th {
  padding-bottom: 16px;
  color: var(--header);
  text-transform: capitalize;
}
.main-cart-wrapper .cart-wrapper .cart-items-wrapper table tbody .cart-item td {
  border-bottom: 1px solid var(--border);
  padding: 16px 0;
}
.main-cart-wrapper
  .cart-wrapper
  .cart-items-wrapper
  table
  tbody
  .cart-item
  .cart-item-info {
  display: flex;
  align-items: center;
  gap: 16px;
}
.main-cart-wrapper
  .cart-wrapper
  .cart-items-wrapper
  table
  tbody
  .cart-item
  .cart-item-price {
  color: var(--header);
}
.main-cart-wrapper
  .cart-wrapper
  .cart-items-wrapper
  table
  tbody
  .cart-item
  .cart-item-price-2 {
  color: var(--header);
}
.main-cart-wrapper
  .cart-wrapper
  .cart-items-wrapper
  table
  tbody
  .cart-item
  .cart-item-price-2
  .total-prices {
  color: var(--header);
}
.main-cart-wrapper
  .cart-wrapper
  .cart-items-wrapper
  table
  tbody
  .cart-item
  .cart-item-quantity {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background: var(--bg);
  width: 100px;
  border-radius: 5px;
  padding: 2px 20px;
}
.main-cart-wrapper
  .cart-wrapper
  .cart-items-wrapper
  table
  tbody
  .cart-item
  .cart-item-quantity
  span {
  display: block;
  color: var(--header);
}
.main-cart-wrapper
  .cart-wrapper
  .cart-items-wrapper
  table
  tbody
  .cart-item
  .cart-item-quantity
  .cart-item-quantity-controller
  a {
  display: block;
}
.main-cart-wrapper
  .cart-wrapper
  .cart-items-wrapper
  table
  tbody
  .cart-item
  .cart-item-quantity
  .cart-item-quantity-controller
  a
  i {
  color: var(--header);
}
.main-cart-wrapper
  .cart-wrapper
  .cart-items-wrapper
  table
  tbody
  .cart-item
  .quantity-basket
  .qty {
  display: inline-flex;
  align-items: center;
  border: 1px solid var(--border);
  padding: 10px 20px;
  line-height: 1;
  justify-content: space-between;
}
.main-cart-wrapper
  .cart-wrapper
  .cart-items-wrapper
  table
  tbody
  .cart-item
  .quantity-basket
  .qty
  button,
.main-cart-wrapper
  .cart-wrapper
  .cart-items-wrapper
  table
  tbody
  .cart-item
  .quantity-basket
  .qty
  input {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text);
  font-weight: 400;
  font-size: 22px;
}
.main-cart-wrapper
  .cart-wrapper
  .cart-items-wrapper
  table
  tbody
  .cart-item
  .quantity-basket
  .qty
  input {
  text-align: center;
  border-radius: 0;
  border: unset;
  outline: none;
}
.main-cart-wrapper
  .cart-wrapper
  .cart-items-wrapper
  table
  tbody
  .cart-item
  .cart-item-remove
  a
  i {
  color: var(--header);
}
.main-cart-wrapper .cart-wrapper-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}
.main-cart-wrapper .cart-wrapper-footer form {
  border: 1px solid var(--border);
  padding: 8px;
  border-radius: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.main-cart-wrapper .cart-wrapper-footer form input {
  padding: 5px 5px;
  border: none;
  text-transform: capitalize;
  font-size: 16px;
  outline: none;
  background: transparent;
  color: var(--text);
}
.main-cart-wrapper .cart-wrapper-footer form button {
  outline: none;
  border: none;
}
.main-cart-wrapper .cart-pragh-box {
  margin-top: 24px;
  padding-right: 30px;
}
.main-cart-wrapper .cart-pragh-box .theme-btn {
  border-radius: 0;
}
.main-cart-wrapper .cart-graph {
  border: 1px solid var(--border);
  padding: 30px 30px;
  border-radius: 5px;
}
.main-cart-wrapper .cart-graph h4 {
  text-align: center;
  color: var(--header);
  margin-bottom: 30px;
}
.main-cart-wrapper .cart-graph ul {
  margin-bottom: 30px;
}
.main-cart-wrapper .cart-graph ul li {
  display: flex;
}
.main-cart-wrapper .cart-graph ul li:not(:last-child) {
  border-bottom: 1px solid var(--border);
}
.main-cart-wrapper .cart-graph ul li span {
  width: 50%;
  color: var(--header);
  font-size: 18px;
  text-transform: capitalize;
  padding: 16px 0;
  font-weight: 500;
}

@media (max-width: 767px) {
  .cart-wrapper {
    overflow-x: scroll;
  }
  .cart-wrapper .cart-items-wrapper {
    width: 700px;
  }
  .cart-wrapper .cart-wrapper-footer {
    width: 700px;
  }
}
.checkout-radio {
  box-shadow: 0px 6px 30px rgba(0, 0, 0, 0.06);
  border-radius: 5px;
  padding: 24px;
}
.checkout-radio .primary-text {
  font-size: 24px;
  font-weight: 500;
  line-height: 150%;
  margin-bottom: 16px;
  color: var(--header);
  text-transform: capitalize;
}
.checkout-radio h4 {
  color: var(--header);
  margin-bottom: 16px;
  font-weight: 600;
}
.checkout-radio
  .checkout-radio-wrapper
  .checkout-radio-single
  .form-check-input {
  border-radius: 50%;
  width: 18px;
  height: 18px;
  box-shadow: none;
  outline: none;
  border: 1px solid var(--border);
  font-weight: 500;
}
.checkout-radio .checkout-radio-wrapper .checkout-radio-single label {
  color: var(--header);
  text-transform: capitalize;
}
.checkout-radio
  .checkout-radio-wrapper
  .checkout-radio-single:not(:last-child) {
  margin-bottom: 12px;
}
@media (max-width: 500px) {
  .checkout-radio {
    padding: 10px;
  }
}

.checkout-single-wrapper .checkout-single h4 {
  color: var(--header);
  margin-bottom: 2rem;
  font-weight: 600;
}
.checkout-single-wrapper
  .checkout-single
  .checkout-single-form
  .input-single
  textarea,
.checkout-single-wrapper
  .checkout-single
  .checkout-single-form
  .input-single
  .country-select,
.checkout-single-wrapper
  .checkout-single
  .checkout-single-form
  .input-single
  input {
  width: 100%;
  outline: none;
  box-shadow: none;
  border: 1px solid var(--border);
  border-radius: 5px;
  padding: 12px 24px;
  color: var(--header);
  text-transform: capitalize;
  font-weight: 500;
  background-color: transparent;
}
.checkout-single-wrapper
  .checkout-single
  .checkout-single-form
  .input-single
  label {
  color: var(--header);
  font-size: 18px;
  text-transform: capitalize;
  margin-bottom: 10px;
  font-weight: 500;
}
.checkout-single-wrapper
  .checkout-single
  .checkout-single-form
  .input-single
  ::placeholder {
  color: var(--header);
}
.checkout-single-wrapper
  .checkout-single
  .checkout-single-form
  .input-single
  .nice-select {
  background-color: transparent;
}
.checkout-single-wrapper
  .checkout-single
  .checkout-single-form
  .input-single
  .nice-select
  span {
  font-size: 18px;
  color: var(--header);
  font-weight: 500;
  text-transform: capitalize;
}
.checkout-single-wrapper
  .checkout-single
  .checkout-single-form
  .input-single
  .nice-select::after {
  border-right: 1px solid var(--header);
  border-bottom: 1px solid var(--header);
  right: 25px;
  top: 28px;
}
.checkout-single-wrapper
  .checkout-single
  .checkout-single-form
  .input-single
  .nice-select
  .list {
  right: 0;
  background-color: var(--white);
}
.checkout-single-wrapper
  .checkout-single
  .checkout-single-form
  .input-single
  .nice-select
  .option {
  border: none;
  padding: 6px 15px;
}
.checkout-single-wrapper .checkout-single .checkout-single-form .payment {
  color: var(--header);
  margin-bottom: 12px;
  text-transform: capitalize;
}
.checkout-single-wrapper .boxshado-single {
  box-shadow: 0px 6px 30px rgba(0, 0, 0, 0.06);
  border-radius: 5px;
  padding: 32px;
  margin-bottom: 24px;
}
@media (max-width: 575px) {
  .checkout-single-wrapper .boxshado-single {
    padding: 14px;
  }
}
.checkout-single-wrapper .checkout-single-bg {
  box-shadow: 0px 6px 30px rgba(0, 0, 0, 0.06);
  border-radius: 5px;
  padding: 32px;
}
.checkout-single-wrapper .checkout-single-bg .theme-btn {
  border-radius: 0;
}
.checkout-single-wrapper
  .checkout-single-bg
  .checkout-single-form
  .input-single
  textarea,
.checkout-single-wrapper
  .checkout-single-bg
  .checkout-single-form
  .input-single
  .country-select,
.checkout-single-wrapper
  .checkout-single-bg
  .checkout-single-form
  .input-single
  input {
  border: 1px solid var(--border);
  background: transparent;
  text-transform: capitalize;
}
@media (max-width: 575px) {
  .checkout-single-wrapper .checkout-single-bg {
    padding: 14px;
  }
}
.checkout-single-wrapper .checkout-single-bg .payment-save {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}
.checkout-single-wrapper .checkout-single-bg .payment-save input {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid var(--border);
  background-color: var(--theme);
  outline: none;
  color: var(--header);
}
.checkout-single-wrapper .checkout-single-bg .payment-save label {
  font-size: 16px;
  font-weight: 500;
  text-transform: capitalize;
}

.scrolling-wrap {
  overflow: hidden;
  white-space: nowrap;
  display: flex;
  gap: 20px;
}
.scrolling-wrap .comm {
  display: flex;
  align-items: center;
  animation: scroll 60s linear infinite;
  gap: 30px;
  cursor: pointer;
}
.scrolling-wrap .comm .cmn-textslide {
  font-size: 75px;
  font-weight: 700;
  line-height: 95px;
  text-transform: capitalize;
  color: var(--header);
  font-family: "Satoshi", sans-serif;
}
.scrolling-wrap .comm .cmn-textslide.color-2 {
  color: var(--theme);
}
.scrolling-wrap .comm.cmn-style-2 {
  margin-top: -20px;
}
.scrolling-wrap .comm.cmn-style-2 .cmn-textslide {
  font-size: 100px;
  font-weight: 700;
  color: #e1e4e9;
  line-height: 127px;
}
.scrolling-wrap .comm.cmn-style-2 .cmn-textslide.color-2 {
  color: #ff1b1b;
}
.scrolling-wrap .comm.cmn-style-2 .cmn-textslide.color-3 {
  color: #5b31de;
}
.scrolling-wrap .comm.cmn-style-3 .cmn-textslide {
  text-transform: uppercase;
  font-size: 19px;
  line-height: 65px;
  color: var(--white);
}
.scrolling-wrap .comm.cmn-style-3 img {
  margin-top: -5px;
}
.scrolling-wrap:hover .comm {
  animation-play-state: paused;
}
.scrolling-wrap.bg-style {
  background-color: var(--theme);
}
@media (max-width: 1399px) {
  .scrolling-wrap .comm {
    gap: 20px;
  }
  .scrolling-wrap .comm .cmn-textslide {
    font-size: 60px;
    line-height: 75px;
  }
  .scrolling-wrap .comm img {
    width: 70px;
  }
}
@media (max-width: 991px) {
  .scrolling-wrap {
    gap: 14px;
  }
  .scrolling-wrap .comm {
    gap: 14px;
  }
  .scrolling-wrap .comm .cmn-textslide {
    font-size: 48px;
    line-height: 60px;
  }
  .scrolling-wrap .comm img {
    width: 50px;
    object-fit: contain;
  }
}
@media (max-width: 575px) {
  .scrolling-wrap .comm {
    gap: 12px;
  }
  .scrolling-wrap .comm .cmn-textslide {
    font-size: 36px;
    line-height: 35px;
  }
  .scrolling-wrap .comm img {
    width: 50px;
    object-fit: contain;
  }
}

.mycustom-marque.style-about {
  position: absolute;
  top: 17%;
  left: 0;
  transform: translateY(-50%);
  right: 0;
}
.mycustom-marque.style-about .comm .cmn-textslide {
  color: rgba(71, 97, 164, 0.3);
  font-size: 190px;
  font-weight: 700;
  text-transform: uppercase;
  font-family: "Satoshi", sans-serif;
  line-height: 155px;
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}
@keyframes scroll2 {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-200%);
  }
} /*# sourceMappingURL=main.css.map */
